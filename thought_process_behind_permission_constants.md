<!-- # Thought Process Behind Permission Constants

The `permission.constants.ts` file is a crucial part of structuring a flexible and generic permissions system. Here's an overview of the thought process behind creating it, including explanations of the different components like contexts, scopes, permissions, and conditions.

## Key Components

### 1. Permission Keys
- **PERMISSIONS_KEY**: Represents the main metadata key for storing permissions in reflection data.
- **REQUIRE_ALL_PERMISSIONS_KEY**: Indicates whether all permissions are needed to grant access.
- **PERMISSION_RESOURCE_KEY**: Specifies the type of resource being accessed.
- **PERMISSION_CONTEXT_KEY**: Defines additional context for permission evaluation.
- **PERMISSION_SCOPE_KEY**: Represents the scope of the permission within the system.

### 2. Audit Action Types
These are used to identify the outcome of a permission check.
- **PERMISSION_GRANTED**: Indicates permission has been granted.
- **PERMISSION_DENIED**: Used when permission is denied.
- **PERMISSION_CHECK_FAILED**: Represents a failure during the permission check.
- **PERMISSION_BYPASSED**: Marks permissions that are bypassed for public routes.

### 3. Resource Types
These abstract the different types of resources in the system. They extend the system, allowing it to be flexible enough to handle various resource types (e.g., CASE, DOCUMENT).

### 4. Contexts
Contexts define the circumstances under which permissions are evaluated.
- **TENANT**: Permissions are evaluated within tenant boundaries.
- **MULTI_TENANT**: For permissions spanning multiple tenants.

**Resource-based contexts**:
- **OWNERSHIP**: Permissions related to ownership of resources.
- **ASSIGNMENT**: Specific roles related to assignments.

**Time-based contexts**:
- **TEMPORARY**: For temporary permissions.
- **EXPIRED**: Expired permission states.

**Condition-based contexts**:
- **CONDITIONAL**: Allows for condition-based evaluations.

### 5. Scopes
Scopes help define the breadth or focus of a permission.
- **GLOBAL**: System-wide access.
- **TENANT**: Access within a particular tenant boundary.
- **DEPARTMENT**: Permissions scoped to departments.
- **PERSONAL**: Personal to the user.

### 6. Permission Conditions
These represent conditions under which permissions can be evaluated.
- **Type**: Could be `TIME`, `LOCATION`, or `ATTRIBUTE`.
- **Operator**: Types of logical operators (EQUALS, CONTAINS, etc.). -->

## How Guards and Decorators Work Together

### Permission Guards
The `PermissionGuard` is the enforcement mechanism that reads the metadata set by decorators and evaluates permissions at runtime. Here's how it works:

1. **Metadata Extraction**: The guard uses NestJS's `Reflector` to extract permission metadata from controller methods
2. **Context Building**: It builds a permission context from the request (user, tenant, resource ID, etc.)
3. **Permission Evaluation**: It calls the `GenericPermissionService` to evaluate whether the user has the required permissions
4. **Audit Logging**: Every permission check is logged for compliance and debugging
5. **Access Control**: Returns `true` to allow access or throws `ForbiddenException` to deny

### Decorator Architecture
The decorators work in layers to provide flexibility:

1. **Core Decorator**: `RequirePermissions()` - The foundational decorator that sets all metadata
2. **Generic Resource Decorators**: `CanRead()`, `CanCreate()`, etc. - Work with any resource type
3. **Resource-Specific Decorators**: `CasePermissions.CanView()` - Convenience decorators for common patterns
4. **Complex Decorators**: `RequirePermissionOrAdmin()` - Handle multiple permission scenarios

### Relationship Flow
```
Controller Method
    ↓ (decorated with)
Permission Decorator
    ↓ (sets metadata using)
Permission Constants
    ↓ (read by)
Permission Guard
    ↓ (evaluates using)
Generic Permission Service
    ↓ (logs to)
Audit Service
```

## Resource-Specific Usage Patterns

### CASE Resources
```typescript
// Basic case viewing - tenant context
@CasePermissions.CanView({ context: PermissionContext.TENANT })

// Case editing - requires assignment context
@CasePermissions.CanEdit({ context: PermissionContext.CASE_ASSIGNMENT })

// Case management - admin scope
@CasePermissions.CanManage({ scope: PermissionScope.TENANT })
```

### DOCUMENT Resources
```typescript
// Document viewing - ownership context
@DocumentPermissions.CanView({ context: PermissionContext.DOCUMENT_OWNERSHIP })

// Document sharing - collaboration context
@DocumentPermissions.CanShare({ context: PermissionContext.COLLABORATION })

// Document deletion - requires admin or ownership
@RequirePermissionOrAdmin(ResourceType.DOCUMENT, Permission.DELETE)
```

### USER Resources
```typescript
// User profile viewing - personal scope
@UserPermissions.CanView({ scope: PermissionScope.PERSONAL })

// User management - department scope
@UserPermissions.CanManage({ scope: PermissionScope.DEPARTMENT })

// System user management - global scope
@RequireSystemAdmin({ scope: PermissionScope.GLOBAL })
```

## Example Scenarios with Different Contexts

### Scenario 1: Case Management with Assignment Context
```typescript
@Controller('cases')
export class CaseController {
    // Public case listing - anyone in tenant can view
    @Get()
    @CasePermissions.CanView({ 
        context: PermissionContext.TENANT,
        scope: PermissionScope.TENANT 
    })
    async getCases() { }
    
    // Specific case details - requires assignment or ownership
    @Get(':id/details')
    @CasePermissions.CanView({ 
        context: PermissionContext.CASE_ASSIGNMENT,
        scope: PermissionScope.RESOURCE 
    })
    async getCaseDetails(@Param('id') id: string) { }
    
    // Case editing - must be assigned to case
    @Patch(':id')
    @CasePermissions.CanEdit({ 
        context: PermissionContext.CASE_ASSIGNMENT,
        scope: PermissionScope.RESOURCE 
    })
    async updateCase(@Param('id') id: string) { }
    
    // Case deletion - admin only, any scope
    @Delete(':id')
    @RequirePermissionOrAdmin(ResourceType.CASE, Permission.DELETE, {
        context: PermissionContext.HIERARCHY,
        scope: PermissionScope.TENANT
    })
    async deleteCase(@Param('id') id: string) { }
}
```

### Scenario 2: Document Management with Ownership Context
```typescript
@Controller('documents')
export class DocumentController {
    // Create document - tenant context
    @Post()
    @DocumentPermissions.CanCreate({ 
        context: PermissionContext.TENANT,
        scope: PermissionScope.TENANT 
    })
    async createDocument() { }
    
    // View own documents - ownership context
    @Get('my-documents')
    @DocumentPermissions.CanView({ 
        context: PermissionContext.OWNERSHIP,
        scope: PermissionScope.PERSONAL 
    })
    async getMyDocuments() { }
    
    // Edit document - must own or have collaboration access
    @Patch(':id')
    @DocumentPermissions.CanEdit({ 
        context: PermissionContext.COLLABORATION,
        scope: PermissionScope.RESOURCE 
    })
    async updateDocument(@Param('id') id: string) { }
    
    // Share document - requires ownership or admin
    @Post(':id/share')
    @RequireAnyPermissions([
        RESOURCE_PERMISSIONS.DOCUMENT.SHARE_DOCUMENT,
        RESOURCE_PERMISSIONS.SYSTEM.TENANT_ADMIN
    ], {
        context: PermissionContext.OWNERSHIP,
        scope: PermissionScope.RESOURCE
    })
    async shareDocument(@Param('id') id: string) { }
}
```

### Scenario 3: User Management with Hierarchical Context
```typescript
@Controller('users')
export class UserController {
    // View user profile - personal or assigned context
    @Get(':id/profile')
    @UserPermissions.CanView({ 
        context: PermissionContext.HIERARCHY,
        scope: PermissionScope.PERSONAL 
    })
    async getUserProfile(@Param('id') id: string) { }
    
    // Edit user - department hierarchy context
    @Patch(':id')
    @UserPermissions.CanEdit({ 
        context: PermissionContext.HIERARCHY,
        scope: PermissionScope.DEPARTMENT 
    })
    async updateUser(@Param('id') id: string) { }
    
    // Assign roles - requires admin in tenant context
    @Post(':id/roles')
    @RequireAllPermissions([
        RESOURCE_PERMISSIONS.USER.MANAGE_ROLES,
        RESOURCE_PERMISSIONS.SYSTEM.TENANT_ADMIN
    ], {
        context: PermissionContext.TENANT,
        scope: PermissionScope.TENANT
    })
    async assignUserRoles(@Param('id') id: string) { }
}
```

### Scenario 4: Temporary and Conditional Permissions
```typescript
@Controller('reports')
export class ReportController {
    // Regular report viewing
    @Get(':id')
    @ReportPermissions.CanView({ 
        context: PermissionContext.TENANT,
        scope: PermissionScope.TENANT 
    })
    async getReport(@Param('id') id: string) { }
    
    // Sensitive financial report - temporary access with conditions
    @Get('financial/:id')
    @RequirePermissions([
        RESOURCE_PERMISSIONS.REPORT.VIEW_REPORT,
        'FINANCIAL_DATA_ACCESS'
    ], {
        context: PermissionContext.CONDITIONAL,
        scope: PermissionScope.DEPARTMENT,
        conditions: [
            { type: 'TIME', operator: 'BETWEEN', value: ['09:00', '17:00'] },
            { type: 'ATTRIBUTE', operator: 'EQUALS', field: 'department', value: 'FINANCE' }
        ]
    })
    async getFinancialReport(@Param('id') id: string) { }
    
    // Offshore restricted report - limited scope
    @Get('public/:id')
    @RequireOffshorePermissions({ 
        context: PermissionContext.TENANT,
        scope: PermissionScope.PERSONAL 
    })
    async getPublicReport(@Param('id') id: string) { }
}
```

### Scenario 5: Multi-Context Evaluation
```typescript
@Controller('workflows')
export class WorkflowController {
    // Workflow execution - multiple contexts checked
    @Post(':id/execute')
    @RequireAnyPermissions([
        'WORKFLOW:EXECUTE',
        RESOURCE_PERMISSIONS.SYSTEM.TENANT_ADMIN
    ], {
        context: PermissionContext.ASSIGNMENT, // Primary context
        scope: PermissionScope.RESOURCE
    })
    async executeWorkflow(@Param('id') id: string) {
        // Additional context checks can be performed in the service layer
        // For example, checking if user is assigned to the workflow
        // or if the workflow is in the correct state
    }
}
```

## Context Evaluation Logic

The permission system evaluates contexts in the following order:

1. **System-wide permissions** (SYSTEM_ADMIN, TENANT_ADMIN) - Always granted if present
2. **Resource-specific full access** - Grants all permissions for that resource type
3. **Direct permission match** - Exact permission string match
4. **Contextual evaluation** - Based on the specified context:
   - `OWNERSHIP`: Check if user owns the resource
   - `ASSIGNMENT`: Check if user is assigned to the resource
   - `HIERARCHY`: Check organizational hierarchy
   - `COLLABORATION`: Check collaboration permissions
   - `CONDITIONAL`: Evaluate custom conditions
   - `TEMPORAL`: Check time-based restrictions

we introduce a permission where sales , conveynacing , new client has a view within the system - 

only rule conveyancer admin sees all cases
a super admin should always define rule sets that kind of define how permissions behave 


