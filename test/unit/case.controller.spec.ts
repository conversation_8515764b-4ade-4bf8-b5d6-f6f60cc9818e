import { Test, TestingModule } from '@nestjs/testing';
import { CaseController } from '../../apps/case-management/src/controllers/case.controller';
import { CaseService } from '../../apps/case-management/src/services/case.service';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { CreateCaseDto } from '../../apps/case-management/src/dto/create-case.dto';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { Request } from 'express';

// Mock request with user information
interface RequestWithUser extends Request {
    user: {
        id: string;
        username: string;
        email: string;
        roles: string[];
        systemUserId: string;
        preferred_username: string;
    };
}

describe('CaseController', () => {
    let controller: CaseController;
    let caseService: CaseService;

    // Mock CaseService
    const mockCaseService = {
        createCase: jest.fn(),
        findCaseById: jest.fn(),
        updateCase: jest.fn(),
        deleteCase: jest.fn(),
        findCases: jest.fn(),
        exportCases: jest.fn(),
        generateCaseNumber: jest.fn()
    };

    // Mock request
    const mockRequest = {
        ip: '127.0.0.1',
        headers: {
            'x-forwarded-for': '********'
        },
        user: {
            id: 'auth0|12345',
            systemUserId: 'user-123',
            username: 'testuser',
            preferred_username: 'testuser',
            email: '<EMAIL>',
            roles: ['user']
        }
    } as unknown as RequestWithUser;

    // Mock guards
    const mockJwtGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockTenantGuard = { canActivate: jest.fn().mockReturnValue(true) };
    const mockRolesGuard = { canActivate: jest.fn().mockReturnValue(true) };

    beforeEach(async () => {
        jest.clearAllMocks();

        const module: TestingModule = await Test.createTestingModule({
            controllers: [CaseController],
            providers: [
                {
                    provide: CaseService,
                    useValue: mockCaseService
                }
            ]
        })
            .overrideGuard(JwtGuard)
            .useValue(mockJwtGuard)
            .overrideGuard(TenantGuard)
            .useValue(mockTenantGuard)
            .overrideGuard(RolesGuard)
            .useValue(mockRolesGuard)
            .compile();

        controller = module.get<CaseController>(CaseController);
        caseService = module.get<CaseService>(CaseService);
    });

    it('should be defined', () => {
        expect(controller).toBeDefined();
        expect(caseService).toBeDefined();
    });

    describe('createCase', () => {
        it('should create a new case', async () => {
            // Arrange
            const createCaseDto: CreateCaseDto = {
                title: 'Test Case',
                description: 'Test description',
                clientId: 'client-123',
                priority: CasePriority.MEDIUM,
                type: CaseType.OTHER,
                status: CaseStatus.DRAFT
            };

            const createdCase = {
                id: 'case-123',
                caseNumber: 'CASE-2023-001',
                ...createCaseDto,
                createdBy: mockRequest.user.systemUserId,
                createdAt: new Date()
            };

            mockCaseService.createCase.mockResolvedValue(createdCase);

            // Act
            const result = await controller.createCase(createCaseDto, mockRequest);

            // Assert
            expect(mockCaseService.createCase).toHaveBeenCalledWith(
                createCaseDto,
                mockRequest.user.systemUserId,
                mockRequest.user.preferred_username || mockRequest.user.email,
                mockRequest
            );
            expect(result).toEqual({
                code: 201,
                status: 'Created',
                message: 'Case created successfully',
                data: createdCase
            });
        });
    });

    describe('getCases', () => {
        it('should return a list of cases', async () => {
            // Arrange
            const filterDto = {
                page: 1,
                limit: 10
            };

            const casesData = {
                data: [
                    { id: 'case-1', title: 'Case 1' },
                    { id: 'case-2', title: 'Case 2' }
                ],
                meta: {
                    pagination: {
                        total: 2,
                        page: 1,
                        limit: 10,
                        pages: 1
                    }
                }
            };

            mockCaseService.findCases.mockResolvedValue(casesData);

            // Act
            const result = await controller.getCases(filterDto);

            // Assert
            expect(mockCaseService.findCases).toHaveBeenCalledWith(filterDto);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Cases retrieved successfully',
                data: casesData.data,
                meta: { pagination: casesData.meta.pagination }
            });
        });
    });

    describe('getCaseById', () => {
        it('should return a case by ID', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const caseData = {
                id: caseId,
                title: 'Test Case'
            };

            mockCaseService.findCaseById.mockResolvedValue(caseData);

            // Act
            const result = await controller.getCaseById(caseId);

            // Assert
            expect(mockCaseService.findCaseById).toHaveBeenCalledWith(caseId);
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Case retrieved successfully',
                data: caseData
            });
        });
    });

    describe('updateCase', () => {
        it('should update a case', async () => {
            // Arrange
            const caseId = 'case-id-123';
            const updateCaseDto = {
                title: 'Updated Case Title'
            };

            const updatedCase = {
                id: caseId,
                title: updateCaseDto.title,
                updatedAt: new Date()
            };

            mockCaseService.updateCase.mockResolvedValue(updatedCase);

            // Act
            const result = await controller.updateCase(caseId, updateCaseDto, mockRequest);

            // Assert
            expect(mockCaseService.updateCase).toHaveBeenCalledWith(
                caseId,
                updateCaseDto,
                mockRequest.user.systemUserId,
                mockRequest.user.preferred_username || mockRequest.user.email,
                mockRequest
            );
            expect(result).toEqual({
                code: 200,
                status: 'OK',
                message: 'Case updated successfully',
                data: updatedCase
            });
        });
    });
});
