{"name": "tk-lpm-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "build:core": "nest build core", "build:communication": "nest build communication", "build:document-engine": "nest build document-engine", "build:auth": "nest build auth", "build:case-management": "nest build case-management", "build:all": "concurrently \"yarn build:core\" \"yarn build:communication\" \"yarn build:document-engine\" \"yarn build:auth\" \"yarn build:case-management\"", "format": "prettier --config .prettierrc --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start core", "start:dev": "nest start core --watch", "start:debug": "nest start core --debug --watch", "start:prod": "node dist/apps/core/main", "start:core": "nest start core", "start:core:dev": "nest start core --watch", "start:core:prod": "node dist/apps/core/main", "start:communication": "nest start communication", "start:communication:dev": "nest start communication --watch", "start:communication:prod": "node dist/apps/communication/main", "start:document-engine": "nest start document-engine", "start:document-engine:dev": "nest start document-engine --watch", "start:document-engine:prod": "node dist/apps/document-engine/main", "start:auth": "nest start auth", "start:auth:dev": "nest start auth --watch", "start:auth:prod": "node dist/apps/auth/main", "start:case-management": "nest start case-management", "start:case-management:dev": "nest start case-management --watch", "start:case-management:prod": "node dist/apps/case-management/main", "start:all": "concurrently \"yarn start:core\" \"yarn start:communication\" \"yarn start:document-engine\" \"yarn start:auth\" \"yarn start:case-management\"", "start:all:dev": "concurrently \"yarn start:core:dev\" \"yarn start:communication:dev\" \"yarn start:document-engine:dev\" \"yarn start:auth:dev\" \"yarn start:case-management:dev\"", "start:all:prod": "concurrently \"yarn start:core:prod\" \"yarn start:communication:prod\" \"yarn start:document-engine:prod\" \"yarn start:auth:prod\" \"yarn start:case-management:prod\"", "start:microservices": "concurrently \"yarn start:communication\" \"yarn start:document-engine\" \"yarn start:auth\" \"yarn start:case-management\"", "start:microservices:dev": "concurrently \"yarn start:communication:dev\" \"yarn start:document-engine:dev\" \"yarn start:auth:dev\" \"yarn start:case-management:dev\"", "start:microservices:prod": "concurrently \"yarn start:communication:prod\" \"yarn start:document-engine:prod\" \"yarn start:auth:prod\" \"yarn start:case-management:prod\"", "start:gateway": "concurrently \"yarn start:microservices:dev\" \"yarn start:core:dev\"", "start:gateway:prod": "concurrently \"yarn start:microservices:prod\" \"yarn start:core:prod\"", "gateway": "./scripts/gateway.sh", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "yarn test:unit", "test:docker": "scripts/run-tests.sh", "test:unit": "jest --testPathIgnorePatterns=.integration.spec.ts", "test:unit:only": "./scripts/run-unit-tests.sh", "test:unit:docker": "scripts/run-tests.sh unit", "test:case-management": "jest test/unit/case.*spec.ts --coverage", "test:case-management:detailed": "./scripts/run-case-management-tests.sh", "test:create:unit:service": "node -e \"const fs=require('fs');const path=process.argv[1];if(!path) {console.error('Provide a path like: apps/module-name/service-name');process.exit(1);} const template=fs.readFileSync('test/templates/service.unit.spec.ts', 'utf8'); const testName=path.split('/').pop();const testPath='test/unit/' + testName + '.spec.ts';fs.writeFileSync(testPath, template); console.log('Created unit test: ' + testPath);\"", "test:create:unit:controller": "node -e \"const fs=require('fs');const path=process.argv[1];if(!path) {console.error('Provide a path like: apps/module-name/controller-name');process.exit(1);} const template=fs.readFileSync('test/templates/controller.unit.spec.ts', 'utf8'); const testName=path.split('/').pop();const testPath='test/unit/' + testName + '.spec.ts';fs.writeFileSync(testPath, template); console.log('Created unit test: ' + testPath);\"", "prepare": "husky || true", "preinstall": "node -e \"if(process.env.npm_execpath.includes('yarn') === false) throw new Error('Use Yarn instead of NPM')\"", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "yarn typeorm migration:generate -d typeorm.config.ts", "migration:generate:public": "yarn typeorm migration:generate -d typeorm.config.ts libs/common/src/typeorm/migrations/public/public-migration", "migration:generate:tenant": "ts-node -r tsconfig-paths/register scripts/enhanced-tenant-migration.ts generate", "migration:create": "yarn typeorm migration:create", "migration:run": "yarn typeorm migration:run -d typeorm.config.ts", "migration:run:public": "yarn typeorm migration:run -d typeorm.config.ts", "migration:run:all": "yarn migration:run:public && yarn migration:run:tenant", "migration:revert": "yarn typeorm migration:revert -d typeorm.config.ts", "migration:revert:public": "yarn typeorm migration:revert -d typeorm.config.ts", "migration:revert:tenant": "ts-node scripts/revert-tenant-migration.ts", "migration:show": "yarn typeorm migration:show -d typeorm.config.ts", "drop:tenant-schemas": "ts-node scripts/drop-tenant-schemas.ts", "clean:database": "ts-node scripts/clean-database.ts", "fix:schema": "ts-node scripts/fix-schema-issues.ts", "force:fix:schema": "ts-node scripts/force-fix-schema.ts", "drop:dependencies": "ts-node scripts/drop-dependencies.ts", "verify:entities": "ts-node scripts/verify-entities.ts", "migration:tenant:discover": "ts-node -r tsconfig-paths/register scripts/enhanced-tenant-migration.ts discover", "migration:run:tenant": "ts-node -r tsconfig-paths/register scripts/enhanced-tenant-migration.ts run"}, "engines": {"npm": "please-use-yarn"}, "lint-staged": {"*.ts": ["prettier --config .prettierrc --write", "eslint --fix"]}, "dependencies": {"@commitlint/cli": "^19.8.0", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/terminus": "^11.0.0", "@nestjs/testing": "^11.0.1", "@nestjs/typeorm": "^11.0.0", "@sentry/node": "^9.14.0", "@types/bcrypt": "^5.0.2", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cache-manager": "^7.0.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dotenv": "^16.5.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lint-staged": "16.0.0", "load-esm": "^1.0.2", "lru-cache": "^10.2.0", "nest-winston": "^1.10.2", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.22", "winston-transport": "^4.9.0", "winston-transport-sentry-node": "^3.0.0"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@nrwl/nx-cloud": "^19.1.0", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/aws-sdk": "^2.7.4", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "commander": "^12.0.0", "concurrently": "^9.1.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "glob": "^11.0.2", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "nest-postman-sync": "file:../nest-postman-sync/nest-postman-sync-1.0.0.tgz", "node-fetch": "2", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/test/helpers/jest-setup.ts"], "moduleNameMapper": {"^typeorm.config$": "<rootDir>/typeorm.config.ts", "^apps/(.*)$": "<rootDir>/apps/$1", "^libs/(.*)$": "<rootDir>/libs/$1", "^@app/common$": "<rootDir>/libs/common/src", "^@app/common/(.*)$": "<rootDir>/libs/common/src/$1", "^@app/auth/(.*)$": "<rootDir>/apps/auth/src/$1", "^@app/case-management/(.*)$": "<rootDir>/apps/case-management/src/$1"}, "globalSetup": "./test/helpers/jest-global-setup.js"}}