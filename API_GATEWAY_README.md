# API Gateway - Core Service

## Overview

The Core service now acts as an **API Gateway** that routes all incoming requests to the appropriate microservices. This provides a single entry point for all API calls, simplifying client integration and providing centralized request handling.

## Architecture

```
Client Request → Core Service (API Gateway) → Target Microservice
```

### Routing Logic

All requests to the Core service are automatically routed based on the URL path:

- `/api/auth/*` → Auth Service
- `/api/document-engine/*` → Document Engine Service  
- `/api/communication/*` → Communication Service
- `/api/case-management/*` → Case Management Service

## Usage Examples

### Before (Direct Service Calls)
```bash
# Auth endpoints
curl http://localhost:3001/api/auth/login

# Document Engine endpoints  
curl http://localhost:3003/api/document-engine/generate

# Communication endpoints
curl http://localhost:3002/api/communication/send

# Case Management endpoints
curl http://localhost:3004/api/case-management/cases
```

### After (Through API Gateway)
```bash
# All requests go through the Core service
curl http://localhost:3000/api/auth/login
curl http://localhost:3000/api/document-engine/generate
curl http://localhost:3000/api/communication/send
curl http://localhost:3000/api/case-management/cases
```

## Benefits

1. **Single Entry Point**: Clients only need to know the Core service URL
2. **Simplified Configuration**: No need to manage multiple service endpoints
3. **Centralized Monitoring**: All requests flow through one service
4. **Load Balancing Ready**: Easy to add load balancing logic later
5. **Security**: Centralized authentication and authorization
6. **Request/Response Transformation**: Can modify requests/responses if needed

## Configuration

### Environment Variables

The gateway automatically detects the environment:

- **Development**: Routes to `localhost:PORT`
- **Docker/Production**: Routes to container names

### Service Ports (from `service.config.ts`)

- Core (Gateway): `CORE_PORT` (default: 3000)
- Auth: `AUTH_PORT` (default: 3001)
- Communication: `COMMUNICATION_PORT` (default: 3002)
- Document Engine: `DOCUMENT_ENGINE_PORT` (default: 3003)
- Case Management: `CASE_MANAGEMENT_PORT` (default: 3004)

## Request Flow

1. **Client** sends request to Core service: `GET /api/auth/profile`
2. **API Gateway** (Core service):
   - Parses the URL path
   - Identifies target service: `auth`
   - Constructs target URL: `http://localhost:3001/api/auth/profile`
   - Forwards the request with headers, body, and query parameters
3. **Auth Service** processes the request and returns response
4. **API Gateway** forwards the response back to the client

## Error Handling

The gateway provides consistent error responses:

```json
{
  "statusCode": 404,
  "message": "Service 'unknown-service' not found",
  "error": "Not Found"
}
```

Available services are listed in error responses for unmatched routes.

## Health Checks

Health check endpoints remain direct (not proxied):

- Core: `GET /api/health`
- Individual services: `GET /api/{service}/health`

## Development vs Production

### Development Mode
- Services run on `localhost` with different ports
- Direct container-to-container communication not needed

### Production/Docker Mode  
- Services communicate using Docker container names
- Network isolation through Docker network

## Migration Guide

### For Frontend/Client Applications

**Old way:**
```javascript
const authBaseUrl = 'http://localhost:3001/api';
const docEngineBaseUrl = 'http://localhost:3003/api';
const commBaseUrl = 'http://localhost:3002/api';

// Make calls to different services
await fetch(`${authBaseUrl}/auth/login`, { ... });
await fetch(`${docEngineBaseUrl}/document-engine/generate`, { ... });
```

**New way:**
```javascript
const apiBaseUrl = 'http://localhost:3000/api';

// All calls go through the gateway
await fetch(`${apiBaseUrl}/auth/login`, { ... });
await fetch(`${apiBaseUrl}/document-engine/generate`, { ... });
```

### For Service-to-Service Communication

Services can still communicate directly or use the gateway:

```typescript
// Direct communication (faster, for internal calls)
const authResponse = await this.httpService.get('http://localhost:3001/api/auth/verify');

// Through gateway (for consistency)
const authResponse = await this.httpService.get('http://localhost:3000/api/auth/verify');
```

## Monitoring and Debugging

Enable debug logging in the Core service to see routing decisions:

```bash
LOG_LEVEL=debug npm run start:core
```

Logs will show:
```
[ProxyController] Proxying GET /api/auth/profile
[ProxyController] Routing to service: auth, path: /api/auth/profile
```

## Future Enhancements

1. **Rate Limiting**: Add rate limiting per service/endpoint
2. **Caching**: Cache responses for GET requests
3. **Authentication Middleware**: Centralized JWT validation
4. **Request Transformation**: Modify requests before forwarding
5. **Load Balancing**: Route to multiple instances of the same service
6. **Circuit Breaker**: Handle service failures gracefully
7. **API Versioning**: Route different API versions to different service versions

## Troubleshooting

### Service Not Found Error
- Check if the target service is running
- Verify the service name in the URL path
- Ensure environment variables are set correctly

### Connection Refused
- Verify the target service port configuration
- Check if services can communicate (network connectivity)
- For Docker: ensure services are on the same network

### Timeout Errors
- Check if the target service is responding slowly
- Consider increasing the timeout (currently 30 seconds)
- Monitor service health endpoints 