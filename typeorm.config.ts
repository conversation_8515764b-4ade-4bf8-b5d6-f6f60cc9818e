import { DataSource } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables
dotenv.config({
    path: path.resolve(process.cwd(), '.env')
});

// Base configuration shared between public and tenant schemas
export const baseConfig: Partial<PostgresConnectionOptions> = {
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    synchronize: false,
    ssl: process.env.POSTGRES_SSL === 'true'
};

// Public schema configuration
export const publicConfig: PostgresConnectionOptions = {
    ...baseConfig,
    type: 'postgres',
    schema: 'public',
    entities: [
        path.join(__dirname, './libs/common/src/typeorm/entities/public/**/*.entity.{js,ts}')
    ],
    migrations: [path.join(__dirname, './libs/common/src/typeorm/migrations/public/**/*.ts')],
    migrationsTableName: 'typeorm_migrations'
};

// Tenant schema configuration (used by the TenantSchemaMigrationService)
export const tenantConfig: PostgresConnectionOptions = {
    ...baseConfig,
    type: 'postgres',
    entities: [
        path.join(__dirname, './libs/common/src/typeorm/entities/tenant/**/*.entity.{js,ts}')
    ],
    migrations: [path.join(__dirname, './libs/common/src/typeorm/migrations/tenant/**/*.ts')],
    migrationsTableName: 'tenant_typeorm_migrations'
};

// Create and export the DataSource for public schema
export default new DataSource(publicConfig);
