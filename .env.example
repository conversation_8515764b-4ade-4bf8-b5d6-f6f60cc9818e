# Environment
NODE_ENV=development

# Service Ports
CORE_PORT=3000
COMMUNICATION_PORT=3001
DOCUMENT_ENGINE_PORT=3002
AUTH_PORT=3003
CASE_MANAGEMENT_PORT=3004
TASK_MANAGEMENT_PORT=3005
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=tk_lpm
POSTGRES_SSL=false
POSTGRES_MAX_CONNECTIONS=100
POSTGRES_IDLE_TIMEOUT_MS=30000
POSTGRES_CONNECTION_TIMEOUT_MS=2000

# Used by NestJS API service
KEYCLOAK_SERVER_URL=http://localhost:8080
KEYCLOAK_REALM=REALM_NAME
KEYCLOAK_CLIENT_ID=admin-cli
KEYCLOAK_CLIENT_SECRET=ulpN9SOvItx4ncOOUvR0CmIpza4khgMo

# Used by Docker Keycloak container
KEYCLOAK_HOST=localhost
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
KEYCLOAK_HOSTNAME_PORT=8080

# API Configuration
API_GLOBAL_PREFIX=api
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info

# CORS Configuration
CORS_ENABLED=true
CORS_ORIGIN=*

### Testing db 
DB_HOST=localhost
DB_PORT=5433
DB_NAME=test_db
DB_USER=postgres
DB_PASS=postgres


# Redis configuration for caching
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=changeme
REDIS_DB=1