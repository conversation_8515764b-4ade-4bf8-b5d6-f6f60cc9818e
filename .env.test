# Test environment configuration

# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5434
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=tk_lpm_test

# Keycloak 
KEYCLOAK_HOST=localhost
KEYCLOAK_PORT=8090
KEYCLOAK_ADMIN=admin
KE<PERSON>OAK_ADMIN_PASSWORD=admin
KEY<PERSON>OAK_HOSTNAME_PORT=8090
KEYCLOAK_BASE_URL=http://localhost:8090

# JWT
JWT_SECRET=test-secret-key-for-jwt-tokens
JWT_EXPIRY=3600

# System
NODE_ENV=test
PORT=3000
API_PREFIX=api
