import {
    Controller,
    Post,
    Body,
    Get,
    Param,
    Put,
    UseGuards,
    Req,
    Res,
    UnauthorizedException,
    BadRequestException
} from '@nestjs/common';
import { Request as ExpressRequest, Response } from 'express';
import { ApiResponseUtil, ApiResponse } from '@app/common/api-response';
import { CreateTenantDto } from '../dto/create-tenant.dto';
import { CreateRoleGroupDto } from '../dto/create-role-group.dto';
import { UpdateRoleGroupPermissionsDto } from '../dto/update-role-group-permissions.dto';
import { CreateUserDto } from '../dto/create-user.dto';
import { LoginDto } from '../dto/login.dto';
import { RefreshTokenDto } from '../dto/refresh-token.dto';
import { PromoteUserDto } from '../dto/promote-user.dto';
import { SelectTenantDto } from '../dto/select-tenant.dto';
import { UserDto } from '../dto/user.dto';
import { AuthService } from '../services/auth.service';
import { PublicTenant } from '@app/common/multi-tenancy/public-tenant.decorator';
import { TenantGuard } from '@app/common';
import { Roles } from '../../../../libs/common/src/roles/decorators';
import { AppRole } from '@app/common/enums/roles.enums';
import { Tenant } from '@app/common/typeorm/entities/public/tenant.entity';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards';

// Extend Express Request interface to include tenant property
interface Request extends ExpressRequest {
    tenant: Tenant;
}

/**
 * Interface for realm context extracted from request
 */
interface RealmContext {
    realm: string;
    adminPassword?: string;
}

/**
 * Controller for authentication and user management
 */
@Controller()
export class AuthController {
    constructor(private readonly authService: AuthService) {}

    /**
     * Helper method to extract realm context from request
     * @param request The HTTP request
     * @returns The realm context containing realm and optional admin password
     */
    private extractRealmContext(request: Request): RealmContext {
        // Extract realm from request
        const realm = request.cookies?.realm || (request.headers['x-realm'] as string);
        // Extract admin password if provided
        const adminPassword = request.headers['x-admin-password'] as string;

        if (!realm) {
            throw new BadRequestException('Realm is required');
        }

        return { realm, adminPassword };
    }
    /**
     * Creates a new tenant (realm) with an initial admin user
     */
    @Post('create-tenant')
    @PublicTenant()
    async createTenant(@Body() createTenantDto: CreateTenantDto) {
        const result = await this.authService.createTenant(createTenantDto);
        return ApiResponseUtil.created(result, 'Tenant created successfully');
    }

    /**
     * Authenticates a user and returns tokens
     */
    @Post('login')
    async login(@Body() loginDto: LoginDto, @Res({ passthrough: true }) response: Response) {
        // Use the new loginWithoutRealm method if realm is not provided
        const { selectedTenant, ...result } = await this.authService.loginWithoutRealm(
            loginDto,
            response
        );

        if (selectedTenant) {
            response.cookie('selected_tenant_id', selectedTenant.tenantId, {
                httpOnly: true,
                secure: process.env.NODE_ENV !== 'development',
                sameSite: 'lax',
                path: '/',
                maxAge: 24 * 60 * 60 * 1000 // 1 day
            });
        }

        return ApiResponseUtil.ok(result, 'Login successful');
    }

    /**
     * Refreshes tokens using a refresh token
     */
    @Post('refresh')
    async refreshToken(
        @Body() refreshTokenDto: RefreshTokenDto,
        @Req() request: Request,
        @Res({ passthrough: true }) response: Response
    ) {
        // Get refresh token from cookie or request body
        const refreshToken = refreshTokenDto.refreshToken || request.cookies?.refresh_token;
        const realm = refreshTokenDto.realm || request.cookies?.realm;

        if (!refreshToken) {
            throw new BadRequestException('Refresh token is required');
        }

        if (!realm) {
            throw new BadRequestException('Realm is required');
        }

        const result = await this.authService.refreshToken(refreshToken, realm, response);
        return ApiResponseUtil.ok(result, 'Token refreshed successfully');
    }

    /**
     * Logs out a user by clearing auth cookies
     */
    @Post('logout')
    logout(@Res({ passthrough: true }) response: Response) {
        this.authService.clearAuthCookies(response);
        return ApiResponseUtil.ok(null, 'Logged out successfully');
    }

    /**
     * Creates a new user
     * Only SUPER_ADMIN can create users and assign them to role groups
     */
    @Post('users')
    @UseGuards(JwtGuard, RolesGuard, TenantGuard)
    @Roles(AppRole.SUPER_ADMIN, AppRole.ADMIN)
    async createUser(@Body() createUserDto: CreateUserDto, @Req() request: Request) {
        const result = await this.authService.createUser(createUserDto, request.tenant);
        return ApiResponseUtil.created(result, 'User created successfully');
    }

    /**
     * Creates a new role group with permissions (Super Admin only)
     * Role groups define permissions for resources and automatically create user/admin roles
     */
    @Post('role-groups')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.SUPER_ADMIN)
    async createRoleGroup(
        @Body() createRoleGroupDto: CreateRoleGroupDto,
        @Req() request: Request
    ) {
        const { realm, adminPassword } = this.extractRealmContext(request);
        const result = await this.authService.createRoleGroup(createRoleGroupDto, realm, adminPassword);
        return ApiResponseUtil.created(result, 'Role group created successfully');
    }

    /**
     * Gets all role groups and their permissions for the current tenant
     */
    @Get('role-groups')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.ADMIN, AppRole.SUPER_ADMIN)
    async getRoleGroups(@Req() request: Request) {
        const { realm } = this.extractRealmContext(request);
        const result = await this.authService.getRoleGroups(realm);
        return ApiResponseUtil.ok(result, 'Role groups retrieved successfully');
    }

    /**
     * Updates permissions for a specific resource in a role group (Super Admin only)
     */
    @Put('role-groups/:roleGroupKey/permissions')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.SUPER_ADMIN)
    async updateRoleGroupPermissions(
        @Param('roleGroupKey') roleGroupKey: string,
        @Body() updateDto: UpdateRoleGroupPermissionsDto,
        @Req() request: Request
    ) {
        const { realm } = this.extractRealmContext(request);
        await this.authService.updateRoleGroupPermissions(roleGroupKey, updateDto, realm);
        return ApiResponseUtil.ok(null, 'Role group permissions updated successfully');
    }

    /**
     * Gets available resource types and permissions for role group configuration
     */
    @Get('role-groups/permission-options')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.SUPER_ADMIN)
    async getPermissionOptions() {
        const result = this.authService.getAvailablePermissionOptions();
        return ApiResponseUtil.ok(result, 'Permission options retrieved successfully');
    }



    /**
     * Promotes or demotes a user within their role group
     * Super Admin can promote a normal user to admin or demote an admin to user
     */
    @Put('users/:id/promote')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.SUPER_ADMIN)
    async promoteUserInRoleGroup(@Param('id') userId: string, @Body() promoteUserDto: PromoteUserDto) {
        await this.authService.promoteUserInRoleGroup(userId, promoteUserDto);
        const action = promoteUserDto.promoteToAdmin ? 'promoted to admin' : 'demoted to user';
        return ApiResponseUtil.ok(null, `User ${action} in role group '${promoteUserDto.roleGroupKey}' successfully`);
    }

    /**
     * Gets a user by ID
     */
    @Get('users/:id')
    @UseGuards(JwtGuard, RolesGuard)
    async getUserById(
        @Param('id') userId: string,
        @Req() request: Request
    ): Promise<ApiResponse<UserDto>> {
        const { realm, adminPassword } = this.extractRealmContext(request);
        const user = await this.authService.getUserById(realm, userId, adminPassword);
        return ApiResponseUtil.ok(user, 'User retrieved successfully');
    }

    /**
     * Gets a user by username
     */
    @Get('users/username/:username')
    @UseGuards(JwtGuard, RolesGuard)
    async getUserByUsername(
        @Param('username') username: string,
        @Req() request: Request
    ): Promise<ApiResponse<UserDto>> {
        const { realm, adminPassword } = this.extractRealmContext(request);
        const user = await this.authService.getUserByUsername(realm, username, adminPassword);

        if (!user) {
            throw new BadRequestException(
                `User with username '${username}' not found in realm '${realm}'`
            );
        }

        return ApiResponseUtil.ok(user, 'User retrieved successfully');
    }

    /**
     * Gets the current user's profile
     */
    @Get('me')
    @UseGuards(JwtGuard, TenantGuard)
    async getProfile(@Req() request: Request) {
        const user = request['user'];

        const tenant = request.tenant;
        if (!user) {
            throw new UnauthorizedException('User not authenticated');
        }

        // Get the user's roles from the database
        const userRoles = await this.authService.getUserRoles(user.systemUserId, tenant);
        return ApiResponseUtil.ok(
            {
                id: user.systemUserId,
                username: user.preferred_username,
                email: user.email,
                name: user.name,
                firstName: user.given_name,
                lastName: user.family_name,
                emailVerified: user.email_verified,
                roles: userRoles
            },
            'Profile retrieved successfully'
        );
    }

    /**
     * Gets a token using client credentials (service account)
     * This is used for realm admin clients with service accounts enabled
     */
    @Post('client-token/:realm')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.ADMIN, AppRole.SUPER_ADMIN)
    async getClientToken(@Param('realm') realm: string) {
        const result = await this.authService.getClientToken(realm);
        return ApiResponseUtil.ok(result, 'Client token retrieved successfully');
    }

    /**
     * Checks and fixes client configuration for direct access grants
     * This is useful for troubleshooting authentication issues
     */
    @Post('fix-client/:realm/:clientId')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.SUPER_ADMIN)
    async fixClientConfiguration(
        @Param('realm') realm: string,
        @Param('clientId') clientId: string
    ) {
        const result = await this.authService.fixClientConfiguration(realm, clientId);
        return ApiResponseUtil.ok(result, 'Client configuration checked and fixed if needed');
    }

    /**
     * Gets all tenants associated with a user
     */
    @Get('user/tenants')
    @UseGuards(JwtGuard)
    async getUserTenants(@Req() request: Request) {
        const user = request['user'];
        if (!user) {
            throw new UnauthorizedException('User not authenticated');
        }

        // Find the user in our system by their email
        const systemUser = await this.authService.getUserByUsername(
            user.realm_access?.realm || '',
            user.preferred_username
        );

        if (!systemUser) {
            throw new BadRequestException('User not found in the system');
        }

        const userTenants = await this.authService.getUserTenants(systemUser.id);
        return ApiResponseUtil.ok(userTenants, 'User tenants retrieved successfully');
    }

    /**
     * Assigns a user to a tenant with the specified role
     */
    @Post('user/:userId/tenant/:tenantId/role/:roleId')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.ADMIN, AppRole.SUPER_ADMIN)
    async assignUserToTenant(
        @Param('userId') userId: string,
        @Param('tenantId') tenantId: string,
        @Param('roleId') roleId: string
    ) {
        await this.authService.assignUserToTenant(userId, tenantId, roleId);
        return ApiResponseUtil.ok(null, 'User assigned to tenant successfully');
    }

    /**
     * Assigns a user to a tenant without a specific role
     */
    @Post('user/:userId/tenant/:tenantId')
    @UseGuards(JwtGuard, RolesGuard)
    @Roles(AppRole.ADMIN, AppRole.SUPER_ADMIN)
    async assignUserToTenantOnly(
        @Param('userId') userId: string,
        @Param('tenantId') tenantId: string
    ) {
        await this.authService.assignUserToTenant(userId, tenantId);
        return ApiResponseUtil.ok(null, 'User assigned to tenant successfully');
    }

    /**
     * Selects a tenant after login for multi-tenant users
     */
    @Post('select-tenant')
    @UseGuards(JwtGuard)
    async selectTenant(
        @Body() selectTenantDto: SelectTenantDto,
        @Req() request: Request,
        @Res({ passthrough: true }) response: Response
    ) {
        const user = request['user'];
        if (!user) {
            throw new UnauthorizedException('User not authenticated');
        }

        // Get all tenants for this user to validate the selection
        const userTenants = await this.authService.getUserTenants(user.systemUserId);
        const selectedTenant = userTenants.find((t) => t.tenantId === selectTenantDto.tenantId);

        if (!selectedTenant) {
            throw new BadRequestException('Selected tenant is not associated with this user');
        }

        // Set a cookie with the selected tenant
        response.cookie('selected_tenant_id', selectedTenant.tenantId, {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development',
            sameSite: 'lax',
            path: '/',
            maxAge: 24 * 60 * 60 * 1000 // 1 day
        });

        return ApiResponseUtil.ok(selectedTenant, 'Tenant selected successfully');
    }
}
