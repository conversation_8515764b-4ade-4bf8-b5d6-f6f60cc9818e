export interface CreateUserResult {
    id: string;
    username: string;
    email: string;
    firstName?: string;
    lastName?: string;
    enabled: boolean;
    emailVerified: boolean;
    roleGroup: {
        key: string;
        label: string;
        isAdmin: boolean;
        roleName: string;
        permissions: Record<string, string[]>;
    };
    systemRole: {
        id: string;
        name: string;
        description: string;
    };
    tenantProfile: {
        id: string;
        userId: string;
        department?: string;
        createdAt: string;
    };
    keycloakId: string;
    tenantId: string;
    realm: string;
} 