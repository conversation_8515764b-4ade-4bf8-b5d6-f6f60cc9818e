/* eslint-disable @typescript-eslint/prefer-promise-reject-errors */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { AuthConfig } from '@app/common/config/interfaces';
import { HttpStatusCode } from '@app/common/enums/http-status.enum';

/**
 * Error types for Keycloak HTTP service
 */
export enum KeycloakErrorType {
    NETWORK = 'NETWORK',
    CLIENT = 'CLIENT',
    SERVER = 'SERVER',
    UNAUTHORIZED = 'UNAUTHORIZED',
    NOT_FOUND = 'NOT_FOUND',
    TIMEOUT = 'TIMEOUT',
    UNKNOWN = 'UNKNOWN'
}

/**
 * Structured error response for Keycloak HTTP service
 */
export interface KeycloakHttpError {
    type: KeycloakErrorType;
    status?: number;
    message: string;
    originalError?: unknown;
}

/**
 * Retry configuration for Keycloak HTTP service
 */
export interface RetryConfig {
    maxRetries: number;
    initialDelayMs: number;
    maxDelayMs: number;
    retryableErrorTypes: KeycloakErrorType[];
}

/**
 * Circuit breaker configuration
 */
export interface CircuitBreakerConfig {
    failureThreshold: number; // Number of failures before opening circuit
    resetTimeout: number; // Time in milliseconds before trying again
    halfOpenMaxRequests: number; // Max requests in half-open state
}

/**
 * Circuit breaker states
 */
enum CircuitState {
    CLOSED, // Normal operation
    OPEN, // Circuit is open, requests will fail fast
    HALF_OPEN // Testing if service is back online
}

/**
 * Request metrics tracking
 */
interface RequestMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    requestLatencies: number[]; // Store response times in ms
    lastRequestTime: number | null;
    errorCounts: Record<KeycloakErrorType, number>;
}

/**
 * Health check response
 */
export interface HealthCheckResult {
    status: 'UP' | 'DOWN';
    message?: string;
    details?: any;
}

/**
 * Service for making HTTP requests to Keycloak
 * Provides a shared Axios instance with interceptors, retry logic, and error handling
 */
@Injectable()
export class KeycloakHttpService {
    private readonly logger = new Logger(KeycloakHttpService.name);
    private readonly axiosInstance: AxiosInstance;
    private readonly retryConfig: RetryConfig;
    private readonly authConfig: AuthConfig;

    // Circuit breaker implementation
    private readonly circuitConfig: CircuitBreakerConfig;
    private circuitState: CircuitState = CircuitState.CLOSED;
    private failureCount: number = 0;
    private nextAttemptTime: number = 0;
    private halfOpenRequests: number = 0;

    // Metrics tracking
    private metrics: RequestMetrics = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        requestLatencies: [],
        lastRequestTime: null,
        errorCounts: {
            [KeycloakErrorType.NETWORK]: 0,
            [KeycloakErrorType.CLIENT]: 0,
            [KeycloakErrorType.SERVER]: 0,
            [KeycloakErrorType.UNAUTHORIZED]: 0,
            [KeycloakErrorType.NOT_FOUND]: 0,
            [KeycloakErrorType.TIMEOUT]: 0,
            [KeycloakErrorType.UNKNOWN]: 0
        }
    };

    // Keep only the last N request latencies for averaging
    private readonly maxLatenciesToTrack = 100;

    constructor(private readonly configService: ConfigService) {
        this.authConfig = this.configService.get<AuthConfig>('auth')!;

        // Create Axios instance with default configuration
        this.axiosInstance = axios.create({
            baseURL: this.authConfig.serverUrl,
            timeout: 30000, // 30 seconds timeout for longer operations like realm creation
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Ensure interceptors are available and properly initialized
        if (!this.axiosInstance.interceptors) {
            this.axiosInstance.interceptors = {
                request: {
                    use: () => 0,
                    eject: () => {},
                    clear: () => {}
                },
                response: {
                    use: () => 0,
                    eject: () => {},
                    clear: () => {}
                }
            };
            this.logger.warn('Axios interceptors were undefined, created mock interceptors');
        }

        // Configure retry settings from config or defaults
        const configRetry = this.configService.get<RetryConfig>('keycloak.retry');
        this.retryConfig = configRetry || {
            maxRetries: 3,
            initialDelayMs: 300,
            maxDelayMs: 3000,
            retryableErrorTypes: [
                KeycloakErrorType.SERVER,
                KeycloakErrorType.TIMEOUT,
                KeycloakErrorType.NETWORK
            ]
        };

        // Configure circuit breaker from config or defaults
        const configCircuit =
            this.configService.get<CircuitBreakerConfig>('keycloak.circuitBreaker');
        this.circuitConfig = configCircuit || {
            failureThreshold: 5, // Open after 5 consecutive failures
            resetTimeout: 30000, // Try again after 30 seconds
            halfOpenMaxRequests: 3 // Allow 3 requests in half-open state
        };

        // Log the configuration
        this.logger.log(
            `Keycloak HTTP Service initialized with retry config: ${JSON.stringify(this.retryConfig)}`
        );
        this.logger.log(
            `Keycloak HTTP Service initialized with circuit breaker config: ${JSON.stringify(this.circuitConfig)}`
        );

        // Set up request interceptor for logging and metrics
        try {
            this.axiosInstance.interceptors.request.use(
                (config) => {
                    // Track request start time for latency calculation
                    config.headers = config.headers || {};
                    config.headers['request-start-time'] = Date.now().toString();

                    this.metrics.totalRequests++;
                    this.metrics.lastRequestTime = Date.now();

                    this.logger.debug(`Request: ${config.method?.toUpperCase()} ${config.url}`);
                    return config;
                },
                (error) => {
                    this.logger.error(`Request error: ${error.message}`);
                    return Promise.reject(error);
                }
            );

            // Set up response interceptor for logging and error handling
            this.axiosInstance.interceptors.response.use(
                (response) => {
                    // Calculate and track request latency
                    const startTime = parseInt(
                        (response.config.headers?.['request-start-time'] as string) || '0',
                        10
                    );
                    if (startTime > 0) {
                        const latency = Date.now() - startTime;
                        this.trackLatency(latency);
                    }

                    this.metrics.successfulRequests++;

                    this.logger.debug(`Response: ${response.status} ${response.config.url}`);
                    // Reset circuit breaker on successful response
                    this.onRequestSuccess();
                    return response;
                },
                async (error) => {
                    // Calculate and track request latency even for errors
                    if (error.config?.headers?.['request-start-time']) {
                        const startTime = parseInt(
                            error.config.headers['request-start-time'] as string,
                            10
                        );
                        const latency = Date.now() - startTime;
                        this.trackLatency(latency);
                    }

                    const structuredError = this.mapAxiosError(error);
                    this.logger.error(
                        `Response error: ${structuredError.type} - ${structuredError.message}`,
                        structuredError.originalError
                    );

                    // Track error in metrics
                    this.metrics.failedRequests++;
                    this.metrics.errorCounts[structuredError.type]++;

                    // If this is a critical failure, update the circuit breaker
                    if (this.shouldCountAsFailure(structuredError.type)) {
                        this.onRequestFailure();
                    }

                    // Check if we should retry the request
                    if (
                        error.config &&
                        error.config.__retryCount !== undefined &&
                        error.config.__retryCount < this.retryConfig.maxRetries &&
                        this.retryConfig.retryableErrorTypes.includes(structuredError.type)
                    ) {
                        error.config.__retryCount = error.config.__retryCount + 1;

                        // Calculate backoff delay with exponential increase
                        const delay = Math.min(
                            this.retryConfig.initialDelayMs *
                                Math.pow(2, error.config.__retryCount),
                            this.retryConfig.maxDelayMs
                        );

                        this.logger.debug(
                            `Retrying request (${error.config.__retryCount}/${this.retryConfig.maxRetries}) after ${delay}ms: ${error.config.method?.toUpperCase()} ${error.config.url}`
                        );

                        // Wait for the backoff delay
                        await new Promise((resolve) => setTimeout(resolve, delay));

                        // Retry the request
                        return this.axiosInstance(error.config);
                    }

                    return Promise.reject(error);
                }
            );
        } catch (err) {
            this.logger.error('Failed to set up axios interceptors', err);
        }
    }

    /**
     * Track request latency for metrics
     */
    private trackLatency(latencyMs: number): void {
        this.metrics.requestLatencies.push(latencyMs);

        // Keep the array from growing too large by removing oldest entries
        if (this.metrics.requestLatencies.length > this.maxLatenciesToTrack) {
            this.metrics.requestLatencies.shift();
        }
    }

    /**
     * Get current service metrics
     */
    getMetrics(): {
        requestCount: number;
        successRate: number;
        avgLatency: number;
        errorBreakdown: Record<KeycloakErrorType, number>;
        circuitState: string;
    } {
        const totalRequests = this.metrics.totalRequests;
        const successRate =
            totalRequests > 0 ? (this.metrics.successfulRequests / totalRequests) * 100 : 100;

        const avgLatency =
            this.metrics.requestLatencies.length > 0
                ? this.metrics.requestLatencies.reduce((sum, val) => sum + val, 0) /
                  this.metrics.requestLatencies.length
                : 0;

        return {
            requestCount: totalRequests,
            successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
            avgLatency: Math.round(avgLatency),
            errorBreakdown: { ...this.metrics.errorCounts },
            circuitState: CircuitState[this.circuitState]
        };
    }

    /**
     * Reset metrics counters
     */
    resetMetrics(): void {
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            requestLatencies: [],
            lastRequestTime: null,
            errorCounts: {
                [KeycloakErrorType.NETWORK]: 0,
                [KeycloakErrorType.CLIENT]: 0,
                [KeycloakErrorType.SERVER]: 0,
                [KeycloakErrorType.UNAUTHORIZED]: 0,
                [KeycloakErrorType.NOT_FOUND]: 0,
                [KeycloakErrorType.TIMEOUT]: 0,
                [KeycloakErrorType.UNKNOWN]: 0
            }
        };
        this.logger.log('Keycloak HTTP Service metrics reset');
    }

    /**
     * Check if circuit allows making requests
     */
    private canMakeRequest(): boolean {
        const now = Date.now();

        switch (this.circuitState) {
            case CircuitState.CLOSED:
                return true;

            case CircuitState.OPEN:
                // Check if it's time to try again
                if (now >= this.nextAttemptTime) {
                    this.circuitState = CircuitState.HALF_OPEN;
                    this.halfOpenRequests = 0;
                    this.logger.warn('Circuit half-open: testing Keycloak availability');
                    return true;
                }
                return false;

            case CircuitState.HALF_OPEN:
                // Allow limited requests in half-open state
                return this.halfOpenRequests < this.circuitConfig.halfOpenMaxRequests;

            default:
                return true;
        }
    }

    /**
     * Track successful request for circuit breaker
     */
    private onRequestSuccess(): void {
        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.circuitState = CircuitState.CLOSED;
            this.failureCount = 0;
            this.logger.log('Circuit closed: Keycloak service restored');
        } else if (this.circuitState === CircuitState.CLOSED) {
            this.failureCount = 0;
        }
    }

    /**
     * Track failed request for circuit breaker
     */
    private onRequestFailure(): void {
        if (this.circuitState === CircuitState.CLOSED) {
            this.failureCount++;

            if (this.failureCount >= this.circuitConfig.failureThreshold) {
                this.circuitState = CircuitState.OPEN;
                this.nextAttemptTime = Date.now() + this.circuitConfig.resetTimeout;
                this.logger.error(
                    `Circuit opened: Keycloak service unreliable after ${this.failureCount} failures`
                );
            }
        } else if (this.circuitState === CircuitState.HALF_OPEN) {
            this.circuitState = CircuitState.OPEN;
            this.nextAttemptTime = Date.now() + this.circuitConfig.resetTimeout;
            this.logger.warn('Circuit re-opened: Keycloak service still unavailable');
        }
    }

    /**
     * Determine if an error type should count as a failure for circuit breaking
     */
    private shouldCountAsFailure(errorType: KeycloakErrorType): boolean {
        return [
            KeycloakErrorType.NETWORK,
            KeycloakErrorType.SERVER,
            KeycloakErrorType.TIMEOUT
        ].includes(errorType);
    }

    /**
     * Maps Axios errors to structured KeycloakHttpError
     */
    private mapAxiosError(error: AxiosError): KeycloakHttpError {
        // Log the full error for debugging
        this.logger.debug(`Full error object: ${JSON.stringify(error, null, 2)}`);

        // Network errors
        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
            return {
                type: KeycloakErrorType.NETWORK,
                message: `Network error: ${error.message}`,
                originalError: error
            };
        }

        // Timeout errors
        if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
            return {
                type: KeycloakErrorType.TIMEOUT,
                message: `Request timeout: ${error.message}`,
                originalError: error
            };
        }

        // HTTP errors with response
        if (error.response) {
            const { status } = error.response;

            // Authentication errors
            if (status === HttpStatusCode.UNAUTHORIZED || status === HttpStatusCode.FORBIDDEN) {
                return {
                    type: KeycloakErrorType.UNAUTHORIZED,
                    status,
                    message: `Authentication error: ${this.extractErrorMessage(error.response)}`,
                    originalError: error
                };
            }

            // Not found errors
            if (status === HttpStatusCode.NOT_FOUND) {
                return {
                    type: KeycloakErrorType.NOT_FOUND,
                    status,
                    message: `Resource not found: ${this.extractErrorMessage(error.response)}`,
                    originalError: error
                };
            }

            // Client errors (4xx)
            if (
                status >= HttpStatusCode.BAD_REQUEST &&
                status < HttpStatusCode.INTERNAL_SERVER_ERROR
            ) {
                return {
                    type: KeycloakErrorType.CLIENT,
                    status,
                    message: `Client error: ${this.extractErrorMessage(error.response)}`,
                    originalError: error
                };
            }

            // Server errors (5xx)
            if (status >= HttpStatusCode.INTERNAL_SERVER_ERROR) {
                return {
                    type: KeycloakErrorType.SERVER,
                    status,
                    message: `Server error: ${this.extractErrorMessage(error.response)}`,
                    originalError: error
                };
            }
        }

        // Unknown errors
        return {
            type: KeycloakErrorType.UNKNOWN,
            message: `Unknown error: ${error.message}`,
            originalError: error
        };
    }

    /**
     * Extracts error message from Keycloak response
     */
    private extractErrorMessage(response: AxiosResponse): string {
        try {
            const data = response.data;

            // Handle Keycloak error response format
            if (data.error_description) {
                return data.error_description;
            }

            if (data.error) {
                return typeof data.error === 'string' ? data.error : JSON.stringify(data.error);
            }

            if (data.errorMessage) {
                return data.errorMessage;
            }

            // If no specific error format is found, return the status text
            return response.statusText || 'Unknown error';
        } catch (error) {
            this.logger.error('Could not parse error response', error);
            return 'Could not parse error response';
        }
    }

    /**
     * Makes a GET request to Keycloak
     */
    async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
            throw {
                type: KeycloakErrorType.SERVER,
                message: 'Circuit breaker is open - Keycloak service unavailable',
                status: HttpStatusCode.SERVICE_UNAVAILABLE
            } as KeycloakHttpError;
        }

        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.halfOpenRequests++;
        }

        const response = await this.axiosInstance.get<T>(url, config);
        return response.data;
    }

    /**
     * Makes a POST request to Keycloak
     */
    async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
            throw {
                type: KeycloakErrorType.SERVER,
                message: 'Circuit breaker is open - Keycloak service unavailable',
                status: HttpStatusCode.SERVICE_UNAVAILABLE
            } as KeycloakHttpError;
        }

        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.halfOpenRequests++;
        }

        // If data is URLSearchParams, set the correct content type
        if (data instanceof URLSearchParams) {
            config = {
                ...config,
                headers: {
                    ...config?.headers,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            };
        }

        const response = await this.axiosInstance.post<T>(url, data, config);
        return response.data;
    }

    /**
     * Makes a POST request to Keycloak and returns the full response including headers
     */
    async postWithFullResponse<T = any>(
        url: string,
        data?: any,
        config?: AxiosRequestConfig
    ): Promise<AxiosResponse<T>> {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
            throw {
                type: KeycloakErrorType.SERVER,
                message: 'Circuit breaker is open - Keycloak service unavailable',
                status: HttpStatusCode.SERVICE_UNAVAILABLE
            } as KeycloakHttpError;
        }

        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.halfOpenRequests++;
        }

        // If data is URLSearchParams, set the correct content type
        if (data instanceof URLSearchParams) {
            config = {
                ...config,
                headers: {
                    ...config?.headers,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            };
        }

        return await this.axiosInstance.post<T>(url, data, config);
    }

    /**
     * Makes a PUT request to Keycloak
     */
    async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
            throw {
                type: KeycloakErrorType.SERVER,
                message: 'Circuit breaker is open - Keycloak service unavailable',
                status: HttpStatusCode.SERVICE_UNAVAILABLE
            } as KeycloakHttpError;
        }

        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.halfOpenRequests++;
        }

        const response = await this.axiosInstance.put<T>(url, data, config);
        return response.data;
    }

    /**
     * Makes a DELETE request to Keycloak
     */
    async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
        // Check circuit breaker before making request
        if (!this.canMakeRequest()) {
            throw {
                type: KeycloakErrorType.SERVER,
                message: 'Circuit breaker is open - Keycloak service unavailable',
                status: HttpStatusCode.SERVICE_UNAVAILABLE
            } as KeycloakHttpError;
        }

        if (this.circuitState === CircuitState.HALF_OPEN) {
            this.halfOpenRequests++;
        }

        const response = await this.axiosInstance.delete<T>(url, config);
        return response.data;
    }

    /**
     * Gets the Axios instance for direct use
     * Use with caution - prefer the wrapped methods
     */
    getInstance(): AxiosInstance {
        return this.axiosInstance;
    }

    /**
     * Manually reset the circuit breaker
     * Useful for testing or forced reset
     */
    resetCircuitBreaker(): void {
        this.circuitState = CircuitState.CLOSED;
        this.failureCount = 0;
        this.logger.log('Circuit breaker manually reset');
    }

    /**
     * Get current circuit breaker state
     * Useful for health checks and monitoring
     */
    getCircuitState(): { state: string; failureCount: number } {
        return {
            state: CircuitState[this.circuitState],
            failureCount: this.failureCount
        };
    }

    /**
     * Checks the health of the Keycloak server
     * @returns A promise resolving to a health check result
     */
    async checkHealth(): Promise<HealthCheckResult> {
        try {
            if (!this.canMakeRequest()) {
                return {
                    status: 'DOWN',
                    message: 'Circuit breaker is open',
                    details: {
                        circuitState: CircuitState[this.circuitState],
                        failureCount: this.failureCount,
                        nextAttemptTime: new Date(this.nextAttemptTime).toISOString()
                    }
                };
            }

            // Try to access a health or status endpoint if available
            // or just a basic endpoint that should always respond
            await this.get('/health', {
                timeout: 5000 // Short timeout for health checks
                // Initialize retry count in a type-safe way
            } as any).catch(() => {
                // If health endpoint fails, try the root URL
                return this.get('/', {
                    timeout: 5000
                } as any);
            });

            return {
                status: 'UP',
                details: {
                    metrics: this.getMetrics()
                }
            };
        } catch (error) {
            const structuredError = error as KeycloakHttpError;
            return {
                status: 'DOWN',
                message: structuredError.message || 'Failed to connect to Keycloak',
                details: {
                    error: structuredError,
                    metrics: this.getMetrics()
                }
            };
        }
    }

    /**
     * Schedule periodic health checks
     * Should be called during service initialization
     * @param intervalMs Time between health checks in milliseconds
     */
    startPeriodicHealthCheck(intervalMs: number = 600000): void {
        this.logger.log(`Starting periodic Keycloak health checks every ${intervalMs}ms`);

        // Set up interval for health checks
        setInterval(() => {
            (async () => {
                this.logger.debug('Running scheduled Keycloak health check');
                const health = await this.checkHealth();

                if (health.status === 'DOWN') {
                    this.logger.warn('Scheduled health check failed', health.details);
                } else {
                    this.logger.debug('Scheduled health check successful');
                }
            })();
        }, intervalMs);
    }
}
