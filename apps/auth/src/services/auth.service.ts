import {
    Injectable,
    Logger,
    UnauthorizedException,
    BadRequestException,
    ConflictException
} from '@nestjs/common';
import { KeycloakService } from './keycloak.service';
import { CreateTenantDto } from '../dto/create-tenant.dto';
import { LoginDto } from '../dto/login.dto';
import { CreateUserDto } from '../dto/create-user.dto';
import { CreateRoleDto } from '../dto/create-role.dto';
import { AssignRoleDto } from '../dto/assign-role.dto';
import { UserDto } from '../dto/user.dto';
import { KeycloakTokenResponse, JwtTokenClaims } from '../interfaces/keycloak-token.interface';
import { JwksResponse, JsonWebKey } from '../interfaces/jwks.interface';
import { Response } from 'express';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { TenantRepository } from '../../../../libs/common/src/repositories/tenant.repository';
import { UserRepository } from '../../../../libs/common/src/repositories/user.repository';
import { PublicUserRepository } from '../../../../libs/common/src/repositories/public-user.repository';
import { RoleRepository } from '../../../../libs/common/src/repositories/role.repository';
import { TenantSchemaMigrationService } from '@app/common/typeorm/migrations/tenant-schema.migrations';
import { TenantConnectionService, TenantContextService } from '@app/common/multi-tenancy';
import { KeycloakRoleRepresentation } from '../interfaces/keycloak-admin.interface';
import { SystemRoleRepository } from '../../../../libs/common/src/repositories/system-role.repository';
import { TenantRoleRepository } from '../../../../libs/common/src/repositories/tenant-role.repository';
import { PublicSchemaMigrationService } from '@app/common/typeorm/migrations/public-schema.migrations';
import { PasswordUtil } from '@app/common/utils/password.util';
import { AppRole } from '@app/common/enums/roles.enums';
import { Tenant } from '@app/common/typeorm/entities/public/tenant.entity';
import { SystemUser } from '@app/common/typeorm/entities/public/system-user.entity';
import { ROLE_GROUPS } from '@app/common/permissions/role-group-permissions.defaults';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import { CreateUserResult } from '../interfaces/create-user-result.interface';
import { RoleGroupPermissionService } from '@app/common/permissions/role-group-permission.service';
import { CreateRoleGroupDto } from '../dto/create-role-group.dto';
import { UpdateRoleGroupPermissionsDto } from '../dto/update-role-group-permissions.dto';
import { PromoteUserDto } from '../dto/promote-user.dto';
import { RoleGroupDefinition } from '@app/common/permissions/role-group-permissions.defaults';
import { ResourceType } from '@app/common/permissions/permission.constants';

/**
 * Extended JSON Web Key with PEM
 */
interface JsonWebKeyWithPem extends JsonWebKey {
    [key: string]: unknown;
}

/**
 * Cookie options for auth tokens
 */
interface CookieOptions {
    httpOnly: boolean;
    secure: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    maxAge?: number;
    path: string;
}

/**
 * Service for handling authentication logic
 */
@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);
    // private readonly authConfig: AuthConfig;
    private jwksCache: Map<string, { jwks: JwksResponse; timestamp: number }> = new Map();
    private readonly JWKS_CACHE_TTL_MS = 3600000; // 1 hour

    constructor(
        private readonly keycloakService: KeycloakService,
        private readonly tenantRepository: TenantRepository,
        private readonly userRepository: UserRepository,
        private readonly publicUserRepository: PublicUserRepository,
        private readonly roleRepository: RoleRepository,
        private readonly tenantSchemaMigrationService: TenantSchemaMigrationService,
        private readonly tenantContextService: TenantContextService,
        private readonly systemRoleRepository: SystemRoleRepository,
        private readonly tenantRoleRepository: TenantRoleRepository,
        private readonly publicSchemaMigrationService: PublicSchemaMigrationService,
        private readonly tenantConnectionService: TenantConnectionService,
        private readonly roleGroupPermissionService: RoleGroupPermissionService
    ) {}

    /**
     * Creates a new tenant with an initial admin user
     */
    async createTenant(createTenantDto: CreateTenantDto): Promise<{
        realm: string;
        displayName: string;
        adminUsername: string;
        adminPassword: string;
        clientId: string;
    }> {
        let keycloakRealmCreated = false;
        let keycloakRolesCreated = false;
        let keycloakAdminCreated = false;
        let dbTenantCreated = false;
        let tenantSchemaCreated = false;
        let publicUserCreated = false;
        let tenant: Tenant | null = null;
        let publicUser: SystemUser | null = null;

        try {
            // Step 1: Ensure public schema is ready
            const isPublicSchemaReady =
                await this.publicSchemaMigrationService.isPublicSchemaReady();
            if (!isPublicSchemaReady) {
                throw new Error(
                    'Public schema is not ready. Please ensure all migrations are run.'
                );
            }

            // Step 2: Validate tenant doesn't exist
            const existingTenant = await this.tenantRepository.findByRealm(createTenantDto.realm);

            if (existingTenant) {
                throw new ConflictException(
                    `Tenant with realm '${createTenantDto.realm}' already exists`
                );
            }

            // Step 2.5: Check if admin email is already used
            const existingUserWithEmail = await this.publicUserRepository.findByEmail(
                createTenantDto.adminEmail
            );
            if (existingUserWithEmail) {
                throw new ConflictException(
                    `User with email ${createTenantDto.adminEmail} already exists`
                );
            }

            // Step 3: Create realm and initial setup in Keycloak
            await this.keycloakService.createRealm(createTenantDto);
            keycloakRealmCreated = true;
            this.logger.log(`Keycloak realm created: ${createTenantDto.realm}`);

            // Step 4: Create system roles in Keycloak (only SUPER_ADMIN + all group user/admin roles)
            const systemRoles: KeycloakRoleRepresentation[] = [
                { name: 'SUPER_ADMIN', description: 'System Super Admin' }
            ];

            // Add all role group user/admin roles
            for (const group of ROLE_GROUPS) {
                systemRoles.push(
                    { name: `${group.key}_user`, description: `${group.label} User` },
                    { name: `${group.key}_admin`, description: `${group.label} Admin` }
                );
            }

            await this.keycloakService.createRoles(
                createTenantDto.realm,
                createTenantDto.adminPassword,
                systemRoles
            );
            keycloakRolesCreated = true;
            this.logger.log(`Keycloak system roles created for realm: ${createTenantDto.realm}`);

            // Step 5: Create admin user in Keycloak (unchanged)
            const keycloakResult = await this.keycloakService.createInitialAdminUser(
                createTenantDto.realm,
                createTenantDto
            );
            keycloakAdminCreated = true;
            this.logger.log(`Keycloak admin user created for realm: ${createTenantDto.realm}`);

            if (!keycloakResult.client) {
                throw new Error('Failed to create admin client for the realm');
            }

            // Step 6: Save tenant information in public schema
            tenant = await this.tenantRepository.createTenant(
                createTenantDto,
                keycloakResult.client.clientId,
                keycloakResult.client.secret
            );
            dbTenantCreated = true;
            this.logger.log(`Database tenant created: ${tenant.id}`);

            if (!tenant) {
                throw new Error('Failed to create tenant');
            }

            // Step 7: Create and initialize tenant schema
            await this.tenantSchemaMigrationService.createTenantSchema(tenant.id);
            tenantSchemaCreated = true;
            this.logger.log(`Tenant schema created: ${tenant.id}`);

            // Step 8: Validate tenant connection
            const tenantConnection = await this.tenantConnectionService.getTenantDataSource(
                tenant.id
            );
            if (!tenantConnection || !tenantConnection.isInitialized) {
                throw new Error('Failed to establish connection to tenant schema');
            }

            // Step 9: Create SUPER_ADMIN in public schema
            const existingSuperAdmin = await this.systemRoleRepository.findByName('SUPER_ADMIN');
            if (!existingSuperAdmin) {
                await this.systemRoleRepository.create({
                    name: 'SUPER_ADMIN',
                    description: 'System Super Admin',
                    permissions: { '*': ['*'] }
                });
            }

            // Step 9.1: Create ADMIN role in public schema if it doesn't exist
            let tenantAdminRole = await this.systemRoleRepository.findByName(AppRole.ADMIN);
            if (!tenantAdminRole) {
                tenantAdminRole = await this.systemRoleRepository.create({
                    name: AppRole.ADMIN,
                    description: 'Tenant Administrator',
                    permissions: { tenant: ['*'] }
                });
                this.logger.log(`Created ADMIN role in public schema`);
            }

            // Step 10: Create user in public schema (unchanged)
            publicUser = await this.publicUserRepository.createUser({
                email: createTenantDto.adminEmail,
                password: createTenantDto.adminPassword,
                firstName: createTenantDto.adminFirstName,
                lastName: createTenantDto.adminLastName
            });
            publicUserCreated = true;
            this.logger.log(`Public user created: ${publicUser?.id}`);

            // Step 11: Assign TENANT_ADMIN role in public schema

            // First assign the TENANT_ADMIN role to the user
            await this.systemRoleRepository.assignUserToTenant(publicUser.id, tenant.id);

            // Then assign the user to the tenant as a separate operation
            await this.systemRoleRepository.assignRoleToUser(publicUser.id, tenantAdminRole.id);

            // Step 12: Set tenant context for tenant-specific operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: true
            });

            // Step 13: Create role group roles in tenant DB
            // Note: Keycloak roles were already created in Step 4, so we only create tenant DB roles here
            for (const group of ROLE_GROUPS) {
                // User role: default permissions
                await this.tenantRoleRepository.create({
                    name: `${group.key}_user`,
                    description: `${group.label} User`,
                    permissions: group.defaultPermissions
                });
                // Admin role: all permissions for all resources in group
                const adminPermissions: Record<string, Permission[]> = {};
                for (const resource of Object.keys(group.defaultPermissions)) {
                    adminPermissions[resource] = Object.values(Permission);
                }
                await this.tenantRoleRepository.create({
                    name: `${group.key}_admin`,
                    description: `${group.label} Admin`,
                    permissions: adminPermissions
                });
            }
            this.logger.log(`Tenant DB role group roles created for tenant: ${tenant.id}`);

            // Step 14: Create admin profile in tenant schema
            await this.tenantRoleRepository.createUserProfile({
                username: createTenantDto.adminUsername,
                email: createTenantDto.adminEmail,
                userId: publicUser.id,
                firstName: createTenantDto.adminFirstName || '',
                lastName: createTenantDto.adminLastName || '',
                additionalInfo: {
                    isTenantAdmin: true,
                    createdAt: new Date().toISOString(),
                    tenantId: tenant.id,
                    realm: tenant.realm,
                    username: createTenantDto.adminUsername,
                    email: createTenantDto.adminEmail,
                    firstName: createTenantDto.adminFirstName || '',
                    lastName: createTenantDto.adminLastName || '',
                    password: await PasswordUtil.hashPassword(createTenantDto.adminPassword),
                    temporaryPassword: false
                },
                // Assign all {group}_admin roles to the admin user
                roles: ROLE_GROUPS.map((g) => `${g.key}_admin`)
            });

            return {
                realm: createTenantDto.realm,
                displayName: createTenantDto.displayName,
                adminUsername: createTenantDto.adminUsername,
                adminPassword: createTenantDto.adminPassword,
                clientId: keycloakResult.client.clientId
            };
        } catch (error) {
            this.logger.error('Failed to create tenant', error);

            // Rollback operations in reverse order
            try {
                this.logger.log('Starting rollback process...');

                if (publicUserCreated && publicUser) {
                    this.logger.log(`Removing public user: ${publicUser.id}`);
                    try {
                        // Remove user-tenant associations
                        if (tenant) {
                            await this.systemRoleRepository.removeUserFromTenant(
                                publicUser.id,
                                tenant.id
                            );
                        }

                        const tenantAdminRole = await this.systemRoleRepository.findByName(
                            AppRole.ADMIN
                        );

                        if (tenantAdminRole) {
                            // Remove user roles only if the role exists
                            await this.systemRoleRepository.removeRoleFromUser(
                                publicUser.id,
                                tenantAdminRole.id
                            );
                        }

                        // Delete the user
                        await this.publicUserRepository.removeById(publicUser.id);
                    } catch (rollbackError) {
                        this.logger.error(`Failed to remove public user: ${rollbackError.message}`);
                    }
                }

                if (tenantSchemaCreated && tenant) {
                    this.logger.log(`Removing tenant schema for tenant: ${tenant.id}`);
                    try {
                        // Drop the tenant schema
                        // Note: Implement a drop schema method in the migration service
                        const connection = this.tenantConnectionService.getPublicDataSource();
                        const schema = `tenant_${tenant.id.replace(/-/g, '_')}`;
                        await connection.query(`DROP SCHEMA IF EXISTS "${schema}" CASCADE;`);
                    } catch (rollbackError) {
                        this.logger.error(
                            `Failed to remove tenant schema: ${rollbackError.message}`
                        );
                    }
                }

                if (dbTenantCreated && tenant) {
                    this.logger.log(`Removing tenant from database: ${tenant.id}`);
                    try {
                        await this.tenantRepository.removeById(tenant.id);
                    } catch (rollbackError) {
                        this.logger.error(`Failed to remove tenant: ${rollbackError.message}`);
                    }
                }

                if (keycloakAdminCreated) {
                    this.logger.log(
                        `Cleaning up Keycloak admin user: ${createTenantDto.adminUsername}`
                    );
                    try {
                        // Optionally delete the admin user in Keycloak
                        // This might require implementing a deleteUser method in the KeycloakService
                    } catch (rollbackError) {
                        this.logger.error(
                            `Failed to clean up Keycloak admin: ${rollbackError.message}`
                        );
                    }
                }

                if (keycloakRealmCreated) {
                    this.logger.log(`Deleting Keycloak realm: ${createTenantDto.realm}`);
                    try {
                        await this.keycloakService.deleteRealm(createTenantDto.realm);
                    } catch (rollbackError) {
                        this.logger.error(
                            `Failed to delete Keycloak realm: ${rollbackError.message}`
                        );
                    }
                }

                if (keycloakRolesCreated) {
                    this.logger.log(`Deleting Keycloak roles for realm: ${createTenantDto.realm}`);
                }

                this.logger.log('Rollback process completed');
            } catch (rollbackError) {
                this.logger.error('Rollback process failed', rollbackError);
            }

            if (error instanceof ConflictException) {
                throw error;
            }
            throw new BadRequestException(
                `Failed to create tenant: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Authenticates a user and returns tokens
     */
    async login(
        loginDto: LoginDto,
        response: Response
    ): Promise<{ accessToken: string; expiresIn: number }> {
        try {
            // Get the tenant information to find the client credentials
            const tenant = await this.tenantRepository.findByRealm(loginDto.realm!);
            if (!tenant) {
                throw new Error(`Tenant with realm '${loginDto.realm}' not found`);
            }

            // All users in a realm (including realm admin) authenticate using the realm's client
            // For public clients, we don't need to send the client secret
            const isPublicClient = !tenant.clientSecret || tenant.clientSecret === '';

            this.logger.log(`Authenticating user ${loginDto.username} in realm ${loginDto.realm}`);
            this.logger.log(
                `Using client ID: ${tenant.clientId}, client type: ${isPublicClient ? 'public' : 'confidential'}`
            );

            try {
                const tokenResponse = await this.keycloakService.getToken(
                    loginDto.realm!,
                    loginDto.username,
                    loginDto.password,
                    tenant.clientId,
                    isPublicClient ? undefined : tenant.clientSecret
                );

                // Set cookies based on rememberMe flag
                this.setAuthCookies(
                    response,
                    tokenResponse,
                    loginDto.rememberMe ?? false,
                    loginDto.realm!
                );

                // Return access token and expiry
                return {
                    accessToken: tokenResponse.access_token,
                    expiresIn: tokenResponse.expires_in
                };
            } catch (tokenError) {
                // Propagate the specific error from the keycloak service
                this.logger.error('Token acquisition failed', tokenError);
                throw tokenError;
            }
        } catch (error) {
            this.logger.error('Login failed', error);

            // If it's already an UnauthorizedException, just rethrow it
            if (error instanceof UnauthorizedException) {
                throw error;
            }

            // Otherwise, wrap it in a generic message
            throw new UnauthorizedException(
                error instanceof Error ? error.message : 'Authentication failed'
            );
        }
    }

    /**
     * Refreshes tokens using a refresh token
     */
    async refreshToken(
        refreshToken: string,
        realm: string,
        response: Response
    ): Promise<{ accessToken: string; expiresIn: number }> {
        try {
            // Get the tenant information to find the client credentials
            const tenant = await this.tenantRepository.findByRealm(realm);

            if (!tenant) {
                throw new Error(`Tenant with realm '${realm}' not found`);
            }

            // For public clients, we don't need to send the client secret
            const isPublicClient = !tenant.clientSecret || tenant.clientSecret === '';

            this.logger.log(`Refreshing token for realm ${realm}`);
            this.logger.log(
                `Using client ID: ${tenant.clientId}, client type: ${isPublicClient ? 'public' : 'confidential'}`
            );

            // Use the realm's client for token refresh
            const tokenResponse = await this.keycloakService.refreshToken(
                realm,
                refreshToken,
                tenant.clientId,
                isPublicClient ? undefined : tenant.clientSecret
            );

            // Decode the token to check if rememberMe was set
            const decodedToken = jwt.decode(tokenResponse.access_token) as JwtTokenClaims;
            const rememberMe = decodedToken?.remember_me === 'true';

            // Set cookies
            this.setAuthCookies(response, tokenResponse, rememberMe, realm);

            // Return access token and expiry
            return {
                accessToken: tokenResponse.access_token,
                expiresIn: tokenResponse.expires_in
            };
        } catch (error) {
            this.logger.error('Token refresh failed', error);
            throw new UnauthorizedException('Invalid refresh token');
        }
    }

    async getUserByEmail(email: string | undefined) {
        if (!email) {
            return null;
        }
        return this.publicUserRepository.findByEmail(email);
    }

    /**
     * Creates a new user in Keycloak and in the tenant's database
     * or assigns an existing user to a new tenant
     */
    async createUser(createUserDto: CreateUserDto, tenant: Tenant): Promise<CreateUserResult> {
        // Validate role group exists
        const roleGroup = ROLE_GROUPS.find((group) => group.key === createUserDto.roleGroupKey);
        if (!roleGroup) {
            throw new BadRequestException(`Role group '${createUserDto.roleGroupKey}' not found`);
        }

        // Determine the role name based on whether user is a role group admin
        const roleName = createUserDto.isRoleGroupAdmin
            ? `${createUserDto.roleGroupKey}_admin`
            : `${createUserDto.roleGroupKey}_user`;

        this.logger.log(
            `Creating user with role group: ${createUserDto.roleGroupKey}, role: ${roleName}`
        );
        try {
            // Check if user already exists in the public schema
            const existingUser = await this.publicUserRepository.findByEmail(createUserDto.email);

            let publicUser = existingUser;

            // First set tenant context for database operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            if (existingUser) {
                this.logger.log(
                    `Found existing user with email ${createUserDto.email} in public schema`
                );

                // Check if this user already has a profile in this tenant
                try {
                    const tenantProfile = await this.tenantRoleRepository.getUserProfile(
                        existingUser.id
                    );

                    if (tenantProfile) {
                        this.logger.log(`User already has a profile in tenant ${tenant.realm}`);
                        throw new ConflictException(
                            `User with email ${createUserDto.email} already exists in this tenant`
                        );
                    }

                    this.logger.log(
                        `User ${createUserDto.email} exists in public schema but has no profile in tenant ${tenant.realm}`
                    );
                } catch (error) {
                    if (error instanceof ConflictException) {
                        throw error;
                    }
                    // Swallow other errors and continue - assume no profile exists
                    this.logger.warn(`Error checking tenant profile: ${error.message}`);
                }
            } else {
                // Create new user in public schema
                publicUser = await this.publicUserRepository.createUser({
                    email: createUserDto.email,
                    password: createUserDto.password,
                    firstName: createUserDto.firstName,
                    lastName: createUserDto.lastName,
                    isActive: true
                });
                this.logger.log(`Created new user in public schema with id ${publicUser.id}`);
            }

            // At this point publicUser should never be null, but let's add a safety check
            if (!publicUser) {
                throw new Error('Failed to find or create public user record');
            }

            // Find or create the system role for this role group assignment
            let systemRole = await this.systemRoleRepository.findByName(roleName);

            if (!systemRole) {
                // Create the system role if it doesn't exist
                systemRole = await this.systemRoleRepository.create({
                    name: roleName,
                    description: `${roleGroup.label} ${createUserDto.isRoleGroupAdmin ? 'Admin' : 'User'} Role`,
                    permissions: roleGroup.defaultPermissions
                });
                this.logger.log(`Created system role: ${roleName}`);
            }

            // Associate the user with this tenant in the public schema
            await this.systemRoleRepository.assignUserToTenant(publicUser.id, tenant.id);
            this.logger.log(
                `Associated user ${publicUser.id} with tenant ${tenant.id} in public schema`
            );

            // Assign the role group system role to the user
            await this.systemRoleRepository.assignRoleToUser(publicUser.id, systemRole.id);
            this.logger.log(`Assigned system role ${systemRole.name} to user ${publicUser.id}`);

            // Handle Keycloak user creation or lookup
            let keycloakUser;

            try {
                // First try to find the user in Keycloak by email
                const keycloakUsers = await this.keycloakService.findUsersByEmail(
                    tenant.realm,
                    createUserDto.email
                );

                // If user exists in Keycloak, use that
                if (keycloakUsers && keycloakUsers.length > 0) {
                    keycloakUser = keycloakUsers[0];
                    this.logger.log(
                        `Found existing Keycloak user with email ${createUserDto.email} in realm ${tenant.realm}`
                    );
                } else {
                    // User doesn't exist in Keycloak for this realm, create them
                    keycloakUser = await this.keycloakService.createUser(
                        createUserDto,
                        tenant.realm
                    );
                    this.logger.log(
                        `Created new Keycloak user with email ${createUserDto.email} in realm ${tenant.realm}`
                    );
                }
            } catch (error) {
                this.logger.error(`Error finding/creating Keycloak user: ${error.message}`);
                // Attempt to create user as fallback
                keycloakUser = await this.keycloakService.createUser(createUserDto, tenant.realm);
            }

            if (!keycloakUser.id) {
                throw new Error('Failed to create or find user in Keycloak: No user ID returned');
            }

            // Assign role group role in Keycloak
            try {
                await this.keycloakService.assignRolesToUser(keycloakUser.id, {
                    realm: tenant.realm,
                    roles: [roleName],
                    adminPassword: createUserDto.adminPassword
                });
                this.logger.log(`Assigned Keycloak role ${roleName} to user ${keycloakUser.id}`);
            } catch (error) {
                this.logger.error(`Failed to assign Keycloak role: ${error.message}`);
                // Continue with tenant role assignment even if Keycloak fails
            }

            // Find the tenant role to assign
            const tenantRole = await this.tenantRoleRepository.findByName(roleName);
            if (!tenantRole) {
                throw new Error(
                    `Tenant role '${roleName}' not found. Make sure role groups are created during tenant setup.`
                );
            }

            // Double-check again that the user doesn't have a profile in this tenant
            const existingProfile = await this.tenantRoleRepository.getUserProfile(publicUser.id);
            if (existingProfile) {
                this.logger.warn(
                    `User already has a profile in tenant ${tenant.realm}, skipping profile creation`
                );

                // Get existing tenant profile for comprehensive response
                const existingTenantProfile = await this.tenantRoleRepository.getUserProfile(
                    publicUser.id
                );

                // Return comprehensive details even for existing profile
                return {
                    id: keycloakUser.id,
                    username: keycloakUser.username!,
                    email: keycloakUser.email!,
                    firstName: keycloakUser.firstName,
                    lastName: keycloakUser.lastName,
                    enabled: keycloakUser.enabled ?? true,
                    emailVerified: keycloakUser.emailVerified ?? false,
                    roleGroup: {
                        key: createUserDto.roleGroupKey,
                        label: roleGroup.label,
                        isAdmin: createUserDto.isRoleGroupAdmin || false,
                        roleName: roleName,
                        permissions: roleGroup.defaultPermissions
                    },
                    systemRole: {
                        id: systemRole.id,
                        name: systemRole.name,
                        description: systemRole.description
                    },
                    tenantProfile: {
                        id: existingTenantProfile?.id || 'existing',
                        userId: publicUser.id,
                        department: createUserDto.department,
                        createdAt:
                            existingTenantProfile &&
                            typeof existingTenantProfile.createdAt === 'object' &&
                            typeof existingTenantProfile.createdAt.toISOString === 'function'
                                ? existingTenantProfile.createdAt.toISOString()
                                : new Date().toISOString()
                    },
                    keycloakId: keycloakUser.id,
                    tenantId: tenant.id,
                    realm: tenant.realm
                };
            }

            // Create user profile in tenant's database
            this.logger.log(
                `Creating tenant profile for user ${publicUser.id} in tenant ${tenant.realm}`
            );
            const tenantProfile = await this.tenantRoleRepository.createUserProfile({
                username: createUserDto.username,
                email: createUserDto.email,
                userId: publicUser.id,
                firstName: createUserDto.firstName || '',
                lastName: createUserDto.lastName || '',
                additionalInfo: {
                    isTenantAdmin: false,
                    createdAt: new Date().toISOString(),
                    tenantId: tenant.id,
                    realm: tenant.realm,
                    username: createUserDto.username,
                    email: createUserDto.email,
                    firstName: createUserDto.firstName,
                    lastName: createUserDto.lastName,
                    password: await PasswordUtil.hashPassword(createUserDto.password),
                    temporaryPassword: false,
                    roleGroupKey: createUserDto.roleGroupKey,
                    isRoleGroupAdmin: createUserDto.isRoleGroupAdmin || false,
                    department: createUserDto.department
                },
                roles: [roleName]
            });

            // Assign tenant role to user profile
            if (tenantRole && tenantProfile.id && tenantRole.id) {
                await this.tenantRoleRepository.assignRoleToUser(tenantProfile.id, tenantRole.id);
                this.logger.log(
                    `Assigned tenant role '${roleName}' to user profile ${tenantProfile.id}`
                );
            } else {
                if (!tenantProfile.id) {
                    this.logger.error('Cannot assign tenant role: tenantProfile.id is undefined');
                }
                if (!tenantRole.id) {
                    this.logger.error('Cannot assign tenant role: tenantRole.id is undefined');
                }
            }

            this.logger.log(
                `Successfully created tenant profile for user ${publicUser.id} in tenant ${tenant.realm}`
            );

            // Return comprehensive user details
            return {
                id: keycloakUser.id,
                username: keycloakUser.username!,
                email: keycloakUser.email!,
                firstName: keycloakUser.firstName,
                lastName: keycloakUser.lastName,
                enabled: keycloakUser.enabled ?? true,
                emailVerified: keycloakUser.emailVerified ?? false,
                roleGroup: {
                    key: createUserDto.roleGroupKey,
                    label: roleGroup.label,
                    isAdmin: createUserDto.isRoleGroupAdmin || false,
                    roleName: roleName,
                    permissions: roleGroup.defaultPermissions
                },
                systemRole: {
                    id: systemRole.id,
                    name: systemRole.name,
                    description: systemRole.description
                },
                tenantProfile: {
                    id: tenantProfile.id ?? 'unknown',
                    userId: publicUser.id,
                    department: createUserDto.department,
                    createdAt:
                        tenantProfile &&
                        typeof tenantProfile.createdAt === 'object' &&
                        typeof tenantProfile.createdAt.toISOString === 'function'
                            ? tenantProfile.createdAt.toISOString()
                            : new Date().toISOString()
                },
                keycloakId: keycloakUser.id,
                tenantId: tenant.id,
                realm: tenant.realm
            };
        } catch (error) {
            this.logger.error('Failed to create user', error);

            if (error instanceof ConflictException) {
                throw error;
            }

            throw new BadRequestException(
                `Failed to create user: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Creates a new role in Keycloak and in the tenant's database
     */
    async createRole(createRoleDto: CreateRoleDto): Promise<{ id: string; name: string }> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(createRoleDto.realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${createRoleDto.realm}' not found`);
            }

            // Create role in Keycloak
            const keycloakRole = await this.keycloakService.createRole(createRoleDto);

            if (!keycloakRole.id) {
                throw new Error('Failed to create role in Keycloak: No role ID returned');
            }

            // Set tenant context for database operations
            // Note: The TenantContextService will handle sanitizing the tenant ID
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            // Create role in tenant's database
            await this.roleRepository.createRole(createRoleDto);

            return {
                id: keycloakRole.id,
                name: keycloakRole.name
            };
        } catch (error) {
            this.logger.error('Failed to create role', error);
            throw new BadRequestException(
                `Failed to create role: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Assigns roles to a user in Keycloak and in the tenant's database
     */
    async assignRolesToUser(userId: string, assignRoleDto: AssignRoleDto): Promise<void> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(assignRoleDto.realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${assignRoleDto.realm}' not found`);
            }

            // Assign roles in Keycloak
            await this.keycloakService.assignRolesToUser(userId, assignRoleDto);

            // Set tenant context for database operations
            // Note: The TenantContextService will handle sanitizing the tenant ID
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            // Find the user in the tenant's database
            const user = await this.userRepository.findByKeycloakId(userId);
            if (!user) {
                this.logger.warn(`User with Keycloak ID ${userId} not found in tenant database`);
                return;
            }

            // Get the user with roles
            const userWithRoles = await this.userRepository.getUserWithRoles(user.id);
            if (!userWithRoles) {
                this.logger.warn(`User with ID ${user.id} not found in tenant database`);
                return;
            }

            // Find the roles in the tenant's database
            const rolesToAssign: any[] = [];
            for (const roleName of assignRoleDto.roles) {
                const role = await this.roleRepository.findByName(roleName);
                if (role) {
                    rolesToAssign.push(role);
                } else {
                    this.logger.warn(`Role ${roleName} not found in tenant database`);
                }
            }

            // Update the user's roles
            userWithRoles.roles = [...(userWithRoles.roles || []), ...rolesToAssign];
            await this.userRepository.save(userWithRoles);
        } catch (error) {
            this.logger.error('Failed to assign roles', error);
            throw new BadRequestException(
                `Failed to assign roles: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets a user by ID
     * @param realm The realm to search in
     * @param userId The user ID to retrieve
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns The user information
     */
    async getUserById(realm: string, userId: string, adminPassword?: string): Promise<UserDto> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${realm}' not found`);
            }

            const user = await this.keycloakService.getUserById(realm, userId, adminPassword);
            return {
                id: user.id!,
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                enabled: user.enabled,
                emailVerified: user.emailVerified
            };
        } catch (error) {
            this.logger.error('Failed to get user', error);
            throw new BadRequestException(
                `Failed to get user: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets a user by username
     * @param realm The realm to search in
     * @param username The username to search for
     * @param adminPassword Optional admin password for realm-specific admin token
     * @returns The user information or null if not found
     */
    async getUserByUsername(
        realm: string,
        username: string,
        adminPassword?: string
    ): Promise<UserDto | null> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${realm}' not found`);
            }

            const user = await this.keycloakService.getUserByUsername(
                realm,
                username,
                adminPassword
            );

            if (!user) {
                return null;
            }

            return {
                id: user.id!,
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                enabled: user.enabled,
                emailVerified: user.emailVerified
            };
        } catch (error) {
            this.logger.error('Failed to get user by username', error);
            throw new BadRequestException(
                `Failed to get user: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets a token using client credentials (service account)
     * This is used for realm admin clients with service accounts enabled
     */
    async getClientToken(realm: string): Promise<{ accessToken: string; expiresIn: number }> {
        try {
            // Get the tenant information to find the client credentials
            const tenant = await this.tenantRepository.findByRealm(realm);

            if (!tenant) {
                throw new Error(`Tenant with realm '${realm}' not found`);
            }

            // Use the client credentials to get a token
            const tokenResponse = await this.keycloakService.getClientToken(
                realm,
                tenant.clientId,
                tenant.clientSecret
            );

            return {
                accessToken: tokenResponse.access_token,
                expiresIn: tokenResponse.expires_in
            };
        } catch (error) {
            this.logger.error('Failed to get client token', error);
            throw new BadRequestException(
                `Failed to get client token: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Checks and fixes client configuration for direct access grants
     * This is useful for troubleshooting authentication issues
     * @param realm The realm containing the client
     * @param clientId The client ID to check and fix
     * @returns Information about the client configuration
     */
    async fixClientConfiguration(
        realm: string,
        clientId: string
    ): Promise<{
        clientId: string;
        directAccessGrantsEnabled: boolean;
        publicClient: boolean;
        standardFlowEnabled: boolean;
        wasUpdated: boolean;
    }> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${realm}' not found`);
            }

            // Get the original client configuration
            const client = await this.keycloakService.checkAndFixClientConfiguration(
                realm,
                clientId
            );

            // Return simplified client configuration
            return {
                clientId: client.clientId,
                directAccessGrantsEnabled: client.directAccessGrantsEnabled === true,
                publicClient: client.publicClient === true,
                standardFlowEnabled: client.standardFlowEnabled === true,
                wasUpdated:
                    client.directAccessGrantsEnabled === true &&
                    client.publicClient === true &&
                    client.standardFlowEnabled === true
            };
        } catch (error) {
            this.logger.error('Failed to fix client configuration', error);
            throw new BadRequestException(
                `Failed to fix client configuration: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets the JWKS for a realm with caching
     */
    async getJwks(realm: string): Promise<JwksResponse> {
        // Check cache first
        const cachedJwks = this.jwksCache.get(realm);
        const now = Date.now();

        if (cachedJwks && now - cachedJwks.timestamp < this.JWKS_CACHE_TTL_MS) {
            return cachedJwks.jwks;
        }

        // Fetch new JWKS
        try {
            const jwks = await this.keycloakService.getJwks(realm);

            // Cache the result
            this.jwksCache.set(realm, { jwks, timestamp: now });

            return jwks;
        } catch (error) {
            // If we have a cached version, return it even if expired
            if (cachedJwks) {
                this.logger.warn(`Failed to refresh JWKS for realm ${realm}, using cached version`);
                return cachedJwks.jwks;
            }

            // Otherwise, propagate the error
            throw error;
        }
    }

    /**
     * Verifies a JWT token using the public key from JWKS
     * @param token The JWT token to verify
     * @param realm The realm the token belongs to
     * @returns The decoded and verified token claims
     */
    async verifyToken(token: string, realm: string): Promise<JwtTokenClaims> {
        try {
            // Get the JWKS
            const jwks = await this.getJwks(realm);

            // Decode the token header to get the key ID
            const decodedHeader = jwt.decode(token, { complete: true })?.header as
                | { kid?: string }
                | undefined;

            if (!decodedHeader || !decodedHeader.kid) {
                throw new Error('Invalid token header');
            }

            // Find the matching key
            const key = jwks.keys.find((k) => k.kid === decodedHeader.kid);

            if (!key) {
                throw new Error('Key not found in JWKS');
            }

            // Convert the JWK to a PEM certificate
            const publicKey = this.jwkToPem(key as JsonWebKeyWithPem);

            // Verify the token
            return jwt.verify(token, publicKey) as JwtTokenClaims;
        } catch (error) {
            this.logger.error('Token verification failed', error);
            throw new UnauthorizedException('Invalid token');
        }
    }

    /**
     * Extract roles from the token - Updated for role group system
     */
    extractRoles(decodedToken: JwtTokenClaims): string[] {
        const roles: string[] = [];

        try {
            // Add realm roles if they exist (these are the new role group roles)
            if (decodedToken.realm_access && Array.isArray(decodedToken.realm_access.roles)) {
                const realmRoles = decodedToken.realm_access.roles;

                // Filter out default Keycloak roles and keep only our role group roles
                const roleGroupRoles = realmRoles.filter(
                    (role) =>
                        role.endsWith('_user') ||
                        role.endsWith('_admin') ||
                        role === 'SUPER_ADMIN' ||
                        role === 'ADMIN'
                );

                roles.push(...roleGroupRoles);
                this.logger.debug(`Extracted role group roles: ${roleGroupRoles.join(', ')}`);

                // Handle legacy admin role mapping for backward compatibility
                if (realmRoles.includes('admin') && !roles.includes('ADMIN')) {
                    roles.push('ADMIN');
                    this.logger.debug('Added ADMIN role mapping for legacy admin realm role');
                }
            } else {
                this.logger.warn('No realm roles found in token or invalid format');
            }

            // Handle realm-management client roles for super admin privileges
            if (decodedToken.resource_access?.['realm-management']?.roles) {
                const managementRoles = decodedToken.resource_access['realm-management'].roles;

                // If user has realm-admin role, give them SUPER_ADMIN privileges
                if (managementRoles.includes('realm-admin')) {
                    roles.push('SUPER_ADMIN');
                    this.logger.debug('Added SUPER_ADMIN role for realm-admin client role');
                }

                // If user has manage-users or manage-realm role, give them ADMIN privileges
                if (
                    managementRoles.includes('manage-users') ||
                    managementRoles.includes('manage-realm')
                ) {
                    if (!roles.includes('ADMIN')) {
                        roles.push('ADMIN');
                        this.logger.debug('Added ADMIN role for realm management client role');
                    }
                }
            }

            // Check for tenant admin based on email or username patterns
            const isLikelyTenantAdmin =
                decodedToken.preferred_username === 'admin' ||
                (decodedToken.email && decodedToken.email.includes('admin')) ||
                roles.some((role) => role.endsWith('_admin'));

            if (isLikelyTenantAdmin && !roles.includes('ADMIN')) {
                roles.push('ADMIN');
                this.logger.debug('Added ADMIN role based on admin indicators');
            }

            // Deduplicate roles
            const uniqueRoles = [...new Set(roles)];

            // For role group system, if no specific roles found, don't add a default
            // The user should be assigned to a role group during user creation
            if (uniqueRoles.length === 0) {
                this.logger.warn(
                    'No roles found in token - user may need to be assigned to a role group'
                );
                // Return empty array instead of default role - let the system handle unassigned users
                return [];
            }

            this.logger.debug(`Final extracted role group roles: ${uniqueRoles.join(', ')}`);
            return uniqueRoles;
        } catch (error) {
            this.logger.error(`Error extracting roles: ${error.message}`, error.stack);
            // Return empty array on error - let the system handle authentication failure
            return [];
        }
    }

    /**
     * Converts a JWK to a PEM certificate
     * @param jwk The JSON Web Key to convert
     * @returns The PEM certificate as a string
     */
    private jwkToPem(jwk: JsonWebKeyWithPem): string {
        // For RSA keys
        if (jwk.kty === 'RSA') {
            // Convert base64url to base64
            const modulus = this.base64UrlToBase64(jwk.n);
            const exponent = this.base64UrlToBase64(jwk.e);

            // Create a Buffer from the base64 strings
            const modulusBuffer = Buffer.from(modulus, 'base64');
            const exponentBuffer = Buffer.from(exponent, 'base64');

            // Create a node.js key object
            const key = crypto.createPublicKey({
                key: {
                    kty: 'RSA',
                    n: modulusBuffer.toString('base64'),
                    e: exponentBuffer.toString('base64')
                },
                format: 'jwk'
            });

            // Export as PEM
            return key.export({ type: 'spki', format: 'pem' }).toString();
        }

        throw new Error(`Unsupported key type: ${jwk.kty}`);
    }

    /**
     * Converts base64url to base64
     */
    private base64UrlToBase64(base64Url: string): string {
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

        // Add padding if needed
        while (base64.length % 4) {
            base64 += '=';
        }

        return base64;
    }

    /**
     * Sets auth cookies in the response
     */
    private setAuthCookies(
        response: Response,
        tokenResponse: KeycloakTokenResponse,
        rememberMe: boolean,
        realm: string
    ): void {
        // Common cookie options
        const commonOptions: CookieOptions = {
            httpOnly: true,
            secure: process.env.NODE_ENV !== 'development', // Secure in production
            sameSite: 'lax',
            path: '/'
        };

        // Set access token cookie (short-lived)
        response.cookie('access_token', tokenResponse.access_token, {
            ...commonOptions,
            maxAge: tokenResponse.expires_in * 1000
        });

        // Set refresh token cookie (long-lived if rememberMe)
        const refreshTokenOptions: CookieOptions = {
            ...commonOptions,
            maxAge: rememberMe
                ? 30 * 24 * 60 * 60 * 1000 // 30 days
                : tokenResponse.refresh_expires_in * 1000 // Default expiry
        };

        response.cookie('refresh_token', tokenResponse.refresh_token, refreshTokenOptions);

        // Set realm cookie (not httpOnly, needed by frontend)
        response.cookie('realm', realm, {
            secure: process.env.NODE_ENV !== 'development',
            sameSite: 'lax',
            path: '/',
            maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000 // 30 days or 1 day
        });
    }

    /**
     * Clears auth cookies
     */
    clearAuthCookies(response: Response): void {
        response.clearCookie('access_token', { path: '/' });
        response.clearCookie('refresh_token', { path: '/' });
        response.clearCookie('realm', { path: '/' });
    }

    /**
     * Retrieves all tenants associated with a user
     * @param userId The user's ID
     * @returns Array of tenant information
     */
    async getUserTenants(
        userId: string
    ): Promise<{ tenantId: string; realm: string; displayName: string }[]> {
        try {
            return await this.systemRoleRepository.getUserTenants(userId);
        } catch (error) {
            this.logger.error('Failed to get user tenants', error);
            throw new BadRequestException(
                `Failed to get user tenants: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets a user's roles from the database
     * @param userId The ID of the user
     * @returns Array of role names
     */
    async getUserRoles(userId: string, tenant: Tenant): Promise<string[]> {
        try {
            // Check if user has system roles (public schema)
            const userSystemRoles = await this.systemRoleRepository.getUserRoles(userId);
            // Get current tenant context
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.realm,
                enabled: tenant.enabled
            });

            // If tenant context exists, get tenant-specific roles
            let tenantRoles: string[] = [];
            const userTenantRoles = await this.tenantRoleRepository.getUserRoles(userId);
            tenantRoles = userTenantRoles.map((role) => role.name);

            // Combine and deduplicate roles
            const allRoles = [...userSystemRoles.map((role) => role.name), ...tenantRoles];
            return [...new Set(allRoles)];
        } catch (error) {
            this.logger.error(`Error retrieving user roles: ${error.message}`, error.stack);
            return []; // Fallback to basic user role
        }
    }

    /**
     * Associates a user with a tenant and optionally assigns a role
     * @param userId The user's ID
     * @param tenantId The tenant's ID
     * @param roleId Optional role ID to assign
     */
    async assignUserToTenant(userId: string, tenantId: string, roleId?: string): Promise<void> {
        try {
            // Always assign the user to the tenant
            await this.systemRoleRepository.assignUserToTenant(userId, tenantId);

            // Optionally assign a role if provided
            if (roleId) {
                await this.systemRoleRepository.assignRoleToUser(userId, roleId);
            }
        } catch (error) {
            this.logger.error('Failed to assign user to tenant', error);
            throw new BadRequestException(
                `Failed to assign user to tenant: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Promotes or demotes a user within their role group
     * Super Admin can promote a normal user to admin or demote an admin to user
     */
    async promoteUserInRoleGroup(userId: string, promoteUserDto: PromoteUserDto): Promise<void> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(promoteUserDto.realm);
            if (!tenant) {
                throw new BadRequestException(
                    `Tenant with realm '${promoteUserDto.realm}' not found`
                );
            }

            // Validate role group exists
            const roleGroup = ROLE_GROUPS.find((rg) => rg.key === promoteUserDto.roleGroupKey);
            if (!roleGroup) {
                throw new BadRequestException(
                    `Role group '${promoteUserDto.roleGroupKey}' not found`
                );
            }

            // Set tenant context for database operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            // Find the user in the tenant's database
            const user = await this.userRepository.findByKeycloakId(userId);
            if (!user) {
                throw new BadRequestException(
                    `User with Keycloak ID ${userId} not found in tenant database`
                );
            }

            // Get current user roles to check existing role group membership
            const currentTenantRoles = await this.tenantRoleRepository.getUserRoles(user.id);

            // Check if user is currently in this role group
            const currentUserRole = currentTenantRoles.find(
                (role) => role.name === `${promoteUserDto.roleGroupKey}_user`
            );
            const currentAdminRole = currentTenantRoles.find(
                (role) => role.name === `${promoteUserDto.roleGroupKey}_admin`
            );

            if (!currentUserRole && !currentAdminRole) {
                throw new BadRequestException(
                    `User is not a member of the '${promoteUserDto.roleGroupKey}' role group`
                );
            }

            // Determine the target role names
            const userRoleName = `${promoteUserDto.roleGroupKey}_user`;
            const adminRoleName = `${promoteUserDto.roleGroupKey}_admin`;

            if (promoteUserDto.promoteToAdmin) {
                // Promote to admin: Remove user role, add admin role
                if (currentAdminRole) {
                    throw new BadRequestException(
                        `User is already an admin in the '${promoteUserDto.roleGroupKey}' role group`
                    );
                }

                // Remove user role from Keycloak
                if (currentUserRole) {
                    await this.keycloakService.removeRoleFromUser(userId, {
                        realm: promoteUserDto.realm,
                        roles: [userRoleName],
                        adminPassword: promoteUserDto.adminPassword
                    });
                }

                // Add admin role in Keycloak
                await this.keycloakService.assignRolesToUser(userId, {
                    realm: promoteUserDto.realm,
                    roles: [adminRoleName],
                    adminPassword: promoteUserDto.adminPassword
                });

                // Update database roles
                if (currentUserRole) {
                    await this.tenantRoleRepository.removeRoleFromUser(user.id, currentUserRole.id);
                }

                // Find or create admin role in database
                let adminRole = await this.roleRepository.findByName(adminRoleName);
                if (!adminRole) {
                    adminRole = await this.roleRepository.createRole({
                        name: adminRoleName,
                        description: `${roleGroup.label} Admin Role`,
                        realm: promoteUserDto.realm
                    });
                }

                await this.tenantRoleRepository.assignRoleToUser(user.id, adminRole.id);

                this.logger.log(
                    `Promoted user ${userId} to admin in role group '${promoteUserDto.roleGroupKey}'`
                );
            } else {
                // Demote to user: Remove admin role, add user role
                if (!currentAdminRole) {
                    throw new BadRequestException(
                        `User is not an admin in the '${promoteUserDto.roleGroupKey}' role group`
                    );
                }

                // Remove admin role from Keycloak
                await this.keycloakService.removeRoleFromUser(userId, {
                    realm: promoteUserDto.realm,
                    roles: [adminRoleName],
                    adminPassword: promoteUserDto.adminPassword
                });

                // Add user role in Keycloak
                await this.keycloakService.assignRolesToUser(userId, {
                    realm: promoteUserDto.realm,
                    roles: [userRoleName],
                    adminPassword: promoteUserDto.adminPassword
                });

                // Update database roles
                await this.tenantRoleRepository.removeRoleFromUser(user.id, currentAdminRole.id);

                // Find or create user role in database
                let userRole = await this.roleRepository.findByName(userRoleName);
                if (!userRole) {
                    userRole = await this.roleRepository.createRole({
                        name: userRoleName,
                        description: `${roleGroup.label} User Role`,
                        realm: promoteUserDto.realm
                    });
                }

                await this.tenantRoleRepository.assignRoleToUser(user.id, userRole.id);

                this.logger.log(
                    `Demoted user ${userId} to user in role group '${promoteUserDto.roleGroupKey}'`
                );
            }
        } catch (error) {
            this.logger.error('Failed to promote/demote user in role group', error);
            throw new BadRequestException(
                `Failed to promote/demote user: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Authenticates a user and returns tenant selection if multiple tenants exist
     */
    async authenticateAndGetTenants(
        loginDto: LoginDto,
        response: Response
    ): Promise<{
        accessToken: string;
        expiresIn: number;
        userTenants?: { tenantId: string; realm: string; displayName: string }[];
        selectedTenant?: { tenantId: string; realm: string; displayName: string };
    }> {
        const authResult = await this.login(loginDto, response);

        // Decode the token to get user information
        jwt.decode(authResult.accessToken) as JwtTokenClaims;

        // Find the user in our system
        const systemUser = await this.publicUserRepository.findByEmail(loginDto.username);

        if (!systemUser) {
            return authResult;
        }

        // Get all tenants associated with this user
        const userTenants = await this.getUserTenants(systemUser.id);

        // If user belongs to multiple tenants, return the list for selection
        if (userTenants.length > 1) {
            return {
                ...authResult,
                userTenants
            };
        }
        // If user belongs to exactly one tenant, select it automatically
        else if (userTenants.length === 1) {
            return {
                ...authResult,
                selectedTenant: userTenants[0]
            };
        }

        // Otherwise just return the auth result
        return authResult;
    }

    /**
     * Authenticates a user without requiring a realm upfront
     * First checks in system_users table, then gets the appropriate tenant
     */
    async loginWithoutRealm(
        loginDto: LoginDto,
        response: Response
    ): Promise<{
        accessToken: string;
        expiresIn: number;
        userTenants?: { tenantId: string; realm: string; displayName: string }[];
        selectedTenant?: { tenantId: string; realm: string; displayName: string };
    }> {
        try {
            // Check if the user exists in our system_users table
            const systemUser = await this.publicUserRepository.verifyCredentials(
                loginDto.username,
                loginDto.password
            );

            if (!systemUser) {
                throw new UnauthorizedException('Invalid username or password');
            }

            // Get all tenants associated with this user
            const userTenants = await this.systemRoleRepository.getUserTenants(systemUser.id);

            if (userTenants.length === 0) {
                throw new UnauthorizedException('User does not have access to any tenants');
            }

            // If realm is provided, use that specific tenant
            let selectedTenant = loginDto.realm
                ? userTenants.find((t) => t.realm === loginDto.realm)
                : null;

            // If realm not provided or invalid, use the first tenant
            if (!selectedTenant) {
                selectedTenant = userTenants[0];
            }

            // Now login with Keycloak using the selected tenant
            // Get the tenant information to find the client credentials
            const tenant = await this.tenantRepository.findByRealm(selectedTenant.realm);
            if (!tenant) {
                throw new Error(`Tenant with realm '${selectedTenant.realm}' not found`);
            }

            // All users in a realm (including realm admin) authenticate using the realm's client
            // For public clients, we don't need to send the client secret
            const isPublicClient = !tenant.clientSecret || tenant.clientSecret === '';

            this.logger.log(
                `Authenticating user ${loginDto.username} in realm ${selectedTenant.realm}`
            );
            this.logger.log(
                `Using client ID: ${tenant.clientId}, client type: ${isPublicClient ? 'public' : 'confidential'}`
            );

            try {
                const tokenResponse = await this.keycloakService.getToken(
                    selectedTenant.realm,
                    loginDto.username,
                    loginDto.password,
                    tenant.clientId,
                    isPublicClient ? undefined : tenant.clientSecret
                );

                // Set cookies based on rememberMe flag
                this.setAuthCookies(
                    response,
                    tokenResponse,
                    loginDto.rememberMe ?? false,
                    selectedTenant.realm
                );

                // Return access token and expiry along with tenant information
                const result = {
                    accessToken: tokenResponse.access_token,
                    expiresIn: tokenResponse.expires_in
                };

                // If user belongs to multiple tenants, include them in the response
                if (userTenants.length > 1) {
                    return {
                        ...result,
                        userTenants
                    };
                }
                // Otherwise just include the selected tenant
                else {
                    return {
                        ...result,
                        selectedTenant
                    };
                }
            } catch (tokenError) {
                // Propagate the specific error from the keycloak service
                this.logger.error('Token acquisition failed', tokenError);
                throw tokenError;
            }
        } catch (error) {
            this.logger.error('Login failed', error);

            // If it's already an UnauthorizedException, just rethrow it
            if (error instanceof UnauthorizedException) {
                throw error;
            }

            // Otherwise, wrap it in a generic message
            throw new UnauthorizedException(
                error instanceof Error ? error.message : 'Authentication failed'
            );
        }
    }

    /**
     * Creates a new role group with permissions (Super Admin only)
     * This creates both Keycloak roles and tenant database entries
     */
    async createRoleGroup(
        createRoleGroupDto: CreateRoleGroupDto,
        realm: string,
        adminPassword?: string
    ): Promise<{ key: string; label: string; rolesCreated: string[] }> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new BadRequestException(`Tenant with realm '${realm}' not found`);
            }

            // Set tenant context for database operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            // Convert DTO to RoleGroupDefinition
            const defaultPermissions: Record<string, Permission[]> = {};
            for (const resourcePermission of createRoleGroupDto.resourcePermissions) {
                defaultPermissions[resourcePermission.resource] = resourcePermission.permissions;
            }

            const roleGroupDefinition: RoleGroupDefinition = {
                key: createRoleGroupDto.key,
                label: createRoleGroupDto.label,
                defaultPermissions
            };

            // Check if role group already exists
            const exists = await this.roleGroupPermissionService.roleGroupExists(
                createRoleGroupDto.key
            );
            if (exists) {
                throw new BadRequestException(
                    `Role group '${createRoleGroupDto.key}' already exists`
                );
            }

            // Create roles in Keycloak
            const userRoleName = `${createRoleGroupDto.key}_user`;
            const adminRoleName = `${createRoleGroupDto.key}_admin`;

            const keycloakRoles = [
                { name: userRoleName, description: `${createRoleGroupDto.label} User` },
                { name: adminRoleName, description: `${createRoleGroupDto.label} Admin` }
            ];

            await this.keycloakService.createRoles(realm, adminPassword || '', keycloakRoles);
            this.logger.log(`Created Keycloak roles for role group: ${createRoleGroupDto.key}`);

            // Create role group in tenant database
            const result =
                await this.roleGroupPermissionService.createRoleGroup(roleGroupDefinition);

            this.logger.log(
                `Created role group: ${createRoleGroupDto.key} with roles: ${result.rolesCreated.join(', ')}`
            );

            return result;
        } catch (error) {
            this.logger.error('Failed to create role group', error);
            throw new BadRequestException(
                `Failed to create role group: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Updates permissions for a role group resource (Super Admin only)
     */
    async updateRoleGroupPermissions(
        roleGroupKey: string,
        updateDto: UpdateRoleGroupPermissionsDto,
        realm: string
    ): Promise<void> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new BadRequestException(`Tenant with realm '${realm}' not found`);
            }

            // Set tenant context for database operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            // Check if role group exists
            const exists = await this.roleGroupPermissionService.roleGroupExists(roleGroupKey);
            if (!exists) {
                throw new BadRequestException(`Role group '${roleGroupKey}' not found`);
            }

            // Update permissions
            await this.roleGroupPermissionService.setAllowedActions(
                roleGroupKey,
                updateDto.resource,
                updateDto.permissions
            );

            this.logger.log(
                `Updated permissions for role group: ${roleGroupKey}, resource: ${updateDto.resource}`
            );
        } catch (error) {
            this.logger.error('Failed to update role group permissions', error);
            throw new BadRequestException(
                `Failed to update role group permissions: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets all role groups and their permissions for the current tenant
     */
    async getRoleGroups(realm: string): Promise<any[]> {
        try {
            // Check if tenant exists
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (!tenant) {
                throw new BadRequestException(`Tenant with realm '${realm}' not found`);
            }

            // Set tenant context for database operations
            this.tenantContextService.setTenant(tenant.id, {
                id: tenant.id,
                realm: tenant.realm,
                displayName: tenant.displayName || tenant.adminFirstName + '-' + tenant.realm,
                enabled: tenant.enabled
            });

            return await this.roleGroupPermissionService.getAllRoleGroups();
        } catch (error) {
            this.logger.error('Failed to get role groups', error);
            throw new BadRequestException(
                `Failed to get role groups: ${error instanceof Error ? error.message : String(error)}`
            );
        }
    }

    /**
     * Gets available resource types and permissions for role group configuration
     */
    getAvailablePermissionOptions(): {
        resourceTypes: ResourceType[];
        permissions: Permission[];
    } {
        return {
            resourceTypes: this.roleGroupPermissionService.getAvailableResourceTypes(),
            permissions: this.roleGroupPermissionService.getAvailablePermissions()
        };
    }
}
