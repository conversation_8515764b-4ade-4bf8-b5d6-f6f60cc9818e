import {
    Is<PERSON><PERSON>,
    <PERSON>NotEmpty,
    IsEmail,
    IsO<PERSON>al,
    IsBoolean,
    Min<PERSON><PERSON>th,
    Matches,
    IsArray,
    IsE<PERSON>
} from 'class-validator';

/**
 * DTO for creating a new user
 */
export class CreateUserDto {
    /**
     * Username
     */
    @IsString()
    @IsNotEmpty()
    username: string;

    /**
     * Email
     */
    @IsEmail()
    @IsNotEmpty()
    email: string;

    /**
     * First name
     */
    @IsString()
    @IsOptional()
    firstName?: string;

    /**
     * Last name
     */
    @IsString()
    @IsOptional()
    lastName?: string;

    /**
     * Password
     * Must be at least 8 characters and include at least one uppercase letter,
     * one lowercase letter, one number, and one special character
     */
    @IsString()
    @IsNotEmpty()
    @MinLength(8)
    @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/, {
        message:
            'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    })
    password: string;

    /**
     * Whether the user is enabled
     */
    @IsBoolean()
    @IsOptional()
    enabled?: boolean;

    /**
     * Whether the user's email is verified
     */
    @IsBoolean()
    @IsOptional()
    emailVerified?: boolean;

    /**
     * Whether the password is temporary
     */
    @IsBoolean()
    @IsOptional()
    temporaryPassword?: boolean;

    /**
     * Public user ID
     */
    @IsString()
    @IsOptional()
    userId?: string;

    /**
     * Roles to assign to the user
     */
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    roles?: string[];

    /**
     * Role group key that the user belongs to (e.g., 'conveyancers', 'finance')
     */
    @IsString()
    @IsNotEmpty()
    roleGroupKey: string;

    /**
     * Whether the user is an admin within the role group
     */
    @IsBoolean()
    @IsOptional()
    isRoleGroupAdmin?: boolean;

    /**
     * Department for the user (optional)
     */
    @IsString()
    @IsOptional()
    department?: string;

    /**
     * Admin password for the realm admin
     * Only needed when using a realm-specific admin token
     */
    @IsString()
    @IsOptional()
    adminPassword?: string;
}
