FROM node:20-alpine

# Install PostgreSQL client and other tools
RUN apk add --no-cache postgresql-client curl wget

# Set working directory
WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy nest-cli.json
COPY nest-cli.json ./

# Copy the entrypoint script
COPY apps/auth/docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# Create directory for the mounted volume
RUN mkdir -p /app/dist/apps/auth

# Expose the service port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD wget -qO- http://localhost:${PORT:-3003}/api/auth/health || exit 1

# Set the entrypoint
ENTRYPOINT ["/app/docker-entrypoint.sh"]