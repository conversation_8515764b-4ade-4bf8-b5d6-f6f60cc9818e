import {
    Controller,
    Get,
    Post,
    Put,
    Delete,
    Body,
    Param,
    Query,
    UseGuards,
    Req,
    ParseU<PERSON><PERSON>ipe,
    ParseBoolPipe
} from '@nestjs/common';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { HasAnyRole } from '@app/common/roles/decorators';
import { AppRole } from '@app/common/enums/roles.enums';
import { TaskService } from '../services/task.service';
import { TaskAssignmentService } from '../services/task-assignment.service';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { TaskFilterDto } from '../dto/task-filter.dto';
import { TaskStatusChangeDto } from '../dto/task-status-change.dto';
import { AssignTaskDto } from '../dto/assign-task.dto';
import { Request } from 'express';

@Controller('tasks')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class TaskController {
    constructor(
        private readonly taskService: TaskService,
        private readonly taskAssignmentService: TaskAssignmentService
    ) {}

    /**
     * Create a new task
     */
    @Post()
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async createTask(@Body() createTaskDto: CreateTaskDto, @Req() request: Request) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;

        return this.taskService.createTask(createTaskDto, userId, userName);
    }

    /**
     * Get a task by ID
     */
    @Get(':id')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getTaskById(@Param('id', ParseUUIDPipe) id: string, @Req() request: Request) {
        const user = request['user'];
        return this.taskService.getTaskById(id, user.systemUserId);
    }

    /**
     * Get tasks with filtering (requires caseId)
     */
    @Get()
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getTasks(@Query() filterDto: TaskFilterDto) {
        return this.taskService.getTasks(filterDto);
    }

    /**
     * Get tasks for a specific case
     */
    @Get('case/:caseId')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getTasksByCaseId(@Param('caseId', ParseUUIDPipe) caseId: string) {
        return this.taskService.getTasksByCaseId(caseId);
    }

    /**
     * Get tasks for a specific case in a hierarchical structure based on dependencies
     */
    @Get('case/:caseId/hierarchy')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getTasksHierarchyByCaseId(@Param('caseId', ParseUUIDPipe) caseId: string) {
        return this.taskService.getTasksHierarchyByCaseId(caseId);
    }

    /**
     * Update a task
     */
    @Put(':id')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async updateTask(
        @Param('id', ParseUUIDPipe) id: string,
        @Body() updateTaskDto: UpdateTaskDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        return this.taskService.updateTask(id, updateTaskDto, userId);
    }

    /**
     * Change task status
     */
    @Put(':id/status')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async changeTaskStatus(
        @Param('id', ParseUUIDPipe) id: string,
        @Body() statusChangeDto: TaskStatusChangeDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        const userName = user.preferred_username || user.email;
        return this.taskService.changeTaskStatus(id, statusChangeDto, userId, userName);
    }

    /**
     * Assign a task to a user
     */
    @Put(':id/assign')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async assignTask(@Param('id', ParseUUIDPipe) id: string, @Body() assignTaskDto: AssignTaskDto) {
        return this.taskAssignmentService.assignTask(id, assignTaskDto);
    }

    /**
     * Unassign a task
     */
    @Put(':id/unassign')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async unassignTask(@Param('id', ParseUUIDPipe) id: string) {
        return this.taskAssignmentService.unassignTask(id);
    }

    /**
     * Delete a task
     * @param id The task ID
     * @param force Whether to force deletion even if there are dependent tasks
     * @returns True if the task was deleted
     */
    @Delete(':id')
    @HasAnyRole(AppRole.ADMIN)
    async deleteTask(
        @Param('id', ParseUUIDPipe) id: string,
        @Query('force', ParseBoolPipe) force: boolean = false,
        @Req() request: Request
    ) {
        const user = request['user'];
        return this.taskService.deleteTask(id, user.systemUserId, force);
    }
}
