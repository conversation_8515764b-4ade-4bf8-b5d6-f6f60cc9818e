import {
    <PERSON>,
    Get,
    Post,
    Delete,
    Body,
    Param,
    UseGuards,
    Req,
    ParseUUIDPipe
} from '@nestjs/common';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { HasAnyRole } from '@app/common/roles/decorators';
import { AppRole } from '@app/common/enums/roles.enums';
import { TaskDependencyService } from '../services/task-dependency.service';
import { AddDependencyDto } from '../dto/add-dependency.dto';
import { Request } from 'express';

@Controller('tasks/:taskId/dependencies')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class TaskDependencyController {
    constructor(private readonly taskDependencyService: TaskDependencyService) {}

    /**
     * Add a dependency to a task
     */
    @Post()
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async addDependency(
        @Param('taskId', ParseUUIDPipe) taskId: string,
        @Body() addDependencyDto: AddDependencyDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        return this.taskDependencyService.addDependency(taskId, addDependencyDto, userId);
    }

    /**
     * Get dependencies for a task
     */
    @Get()
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getDependencies(@Param('taskId', ParseUUIDPipe) taskId: string) {
        return this.taskDependencyService.getDependencies(taskId);
    }

    /**
     * Get tasks that depend on this task
     */
    @Get('dependents')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async getDependentTasks(@Param('taskId', ParseUUIDPipe) taskId: string) {
        return this.taskDependencyService.getDependentTasks(taskId);
    }

    /**
     * Remove a dependency
     */
    @Delete(':dependencyId')
    @HasAnyRole(AppRole.ADMIN, AppRole.LAWYER)
    async removeDependency(
        @Param('taskId', ParseUUIDPipe) taskId: string,
        @Param('dependencyId', ParseUUIDPipe) dependencyId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        const userId = user.systemUserId;
        return this.taskDependencyService.removeDependency(taskId, dependencyId, userId);
    }
}
