import {
    Injectable,
    NotFoundException,
    BadRequestException,
    ConflictException,
    Inject,
    forwardRef
} from '@nestjs/common';
import { TaskRepository } from '../repositories/task.repository';
import { TaskHistoryRepository } from '../repositories/task-history.repository';
import { TaskDependencyService } from './task-dependency.service';
import { TaskPrioritizationService } from './task-prioritization.service';
import { TaskAssignmentService } from './task-assignment.service';
import { CacheService } from '@app/common/cache/cache.service';
import { CACHE_KEYS } from '@app/common/cache/cache.config';
import { ConfigService } from '@nestjs/config';
import { TenantContextService } from '@app/common/multi-tenancy';
import { instance as logger } from '@app/common/utils';

import { Task, TaskStatus, TaskPriority } from '@app/common/typeorm/entities/tenant/task.entity';
import { TaskDependency } from '@app/common/typeorm/entities/tenant/task-dependency.entity';
import { CreateTaskDto } from '../dto/create-task.dto';
import { UpdateTaskDto } from '../dto/update-task.dto';
import { TaskFilterDto } from '../dto/task-filter.dto';
import { TaskStatusChangeDto } from '../dto/task-status-change.dto';
import { CaseService } from 'apps/case-management/src/services/case.service';
import {
    PaginatedResponse,
    PaginationService
} from 'apps/case-management/src/services/pagination.service';

/**
 * Service for managing tasks
 */
@Injectable()
export class TaskService {
    constructor(
        private readonly taskRepository: TaskRepository,
        private readonly taskHistoryRepository: TaskHistoryRepository,
        private readonly taskDependencyService: TaskDependencyService,
        private readonly taskPrioritizationService: TaskPrioritizationService,
        private readonly taskAssignmentService: TaskAssignmentService,
        private readonly paginationService: PaginationService,
        private readonly cacheService: CacheService,
        private readonly tenantContextService: TenantContextService,
        private readonly configService: ConfigService,
        @Inject(forwardRef(() => CaseService))
        private readonly caseService: CaseService
    ) {}

    /**
     * Creates a new task
     * @param createTaskDto The task creation DTO
     * @param userId The ID of the user creating the task
     * @param userName The name of the user creating the task
     * @returns The created task with dependency information
     */
    async createTask(createTaskDto: CreateTaskDto, userId: string, userName: string): Promise<any> {
        const tenantId = this.tenantContextService.getTenantId();

        // Verify case exists
        try {
            await this.caseService.findCaseById(createTaskDto.caseId, userId);
        } catch (error) {
            throw new NotFoundException(`Case with ID ${createTaskDto.caseId} not found`);
        }

        // Create the task with simplified priority
        const task = await this.taskRepository.create({
            ...createTaskDto,
            status: createTaskDto.status || TaskStatus.OPEN,
            priority:
                createTaskDto.priority ||
                (createTaskDto.dueDate
                    ? this.taskPrioritizationService.suggestPriority({ ...createTaskDto } as Task)
                    : TaskPriority.MEDIUM),
            createdBy: userId
        });

        // Save the task
        const savedTask = await this.taskRepository.save(task);

        // Record initial status in history
        await this.taskHistoryRepository.save({
            taskId: savedTask.id,
            fromStatus: null,
            toStatus: savedTask.status,
            changedBy: userId,
            changedByName: userName,
            metadata: { action: 'TASK_CREATED' }
        });

        // Auto-assign the task if no assignee was specified
        let finalTask = savedTask;
        if (!savedTask.assigneeId) {
            try {
                // Get case details from the case service
                const caseDetails = await this.caseService.getCaseDetails(
                    createTaskDto.caseId,
                    userId
                );
                // Extract user IDs from assignments
                const assigneeIds = caseDetails.assignments.map(
                    (assignment: any) => assignment.userId
                );
                if (assigneeIds.length > 0) {
                    finalTask = await this.taskAssignmentService.autoAssignTask(
                        savedTask,
                        assigneeIds
                    );
                } else {
                    logger.warn(
                        `No assignees found for case ${createTaskDto.caseId}, leaving task unassigned`
                    );
                }
            } catch (error) {
                logger.error(`Error auto-assigning task: ${error.message}`, error.stack);
                logger.warn(`Could not auto-assign task ${savedTask.id}, leaving unassigned`);
            }
        }

        // Cache the task
        try {
            await this.cacheService.set(
                CACHE_KEYS.TASK_BY_ID(tenantId, savedTask.id, userId),
                savedTask
            );
            logger.debug(
                `[CACHE SET] Task by ID (create): key=${CACHE_KEYS.TASK_BY_ID(tenantId, savedTask.id, userId)}`,
                savedTask
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskCreate] tenantId=${tenantId} userId=${userId} taskId=${savedTask.id} cacheKey=${CACHE_KEYS.TASK_BY_ID(tenantId, savedTask.id, userId)} - Failed to set task in cache: ${err?.message}`,
                err
            );
        }

        // Return the task with dependency information (initially empty)
        return this.addDependencyInfoToTask(finalTask, []);
    }
    /**
     * Gets tasks with filtering, pagination, and sorting
     * @param filterDto The filter DTO
     * @returns Paginated response of tasks
     */
    async getTasks(filterDto: TaskFilterDto): Promise<PaginatedResponse<Task>> {
        const [tasks, total] = await this.taskRepository.findWithFilters(filterDto);
        const result = this.paginationService.createPaginatedResponse(
            tasks,
            total,
            filterDto.page || 1,
            filterDto.limit || 10
        );
        return result;
    }

    /**
     * Gets tasks for a specific case with dependency information
     * @param caseId The case ID
     * @param userId The user ID
     * @returns Array of tasks with dependency information
     */
    async getTasksByCaseId(caseId: string): Promise<any[]> {
        // Get tasks with dependency information
        const tasks = await this.getTasksWithDependencyInfo(caseId);
        return tasks;
    }

    /**
     * Gets tasks for a specific case with simplified dependency information
     * @param caseId The case ID
     * @param userId The user ID
     * @returns List of tasks with dependency information
     */
    async getTasksHierarchyByCaseId(caseId: string): Promise<any[]> {
        // Get tasks with dependency information
        const tasks = await this.getTasksWithDependencyInfo(caseId);
        return tasks;
    }

    /**
     * Helper method to get tasks with dependency information
     * @param caseId The case ID
     * @returns Array of tasks with dependency information
     * @private
     */
    private async getTasksWithDependencyInfo(caseId: string): Promise<any[]> {
        // Get all tasks for the case with their dependencies
        const tasks = await this.taskRepository.find({
            where: { caseId },
            relations: ['dependencies', 'dependencies.dependsOn']
        });

        // Create a map to track dependent tasks (tasks that depend on other tasks)
        const dependentTasksMap = new Map<string, string[]>();

        // Initialize the map for all tasks
        tasks.forEach((task) => {
            dependentTasksMap.set(task.id, []);
        });

        // Build dependency relationships
        tasks.forEach((task) => {
            if (task.dependencies && task.dependencies.length > 0) {
                task.dependencies.forEach((dep) => {
                    const dependsOnId = dep.dependsOnId;

                    // Add this task as a dependent of the task it depends on
                    const dependents = dependentTasksMap.get(dependsOnId) || [];
                    dependents.push(task.id);
                    dependentTasksMap.set(dependsOnId, dependents);
                });
            }
        });

        // Enhance tasks with dependency information
        const enhancedTasks = tasks.map((task) => {
            // Get the IDs of tasks that depend on this task
            const dependentTaskIds = dependentTasksMap.get(task.id) || [];

            // Get the IDs of tasks that this task depends on
            const dependsOnTaskIds = (task.dependencies || []).map((dep) => dep.dependsOnId);

            return {
                ...task,
                // Add simplified dependency information
                dependentTasks: dependentTaskIds,
                dependsOnTasks: dependsOnTaskIds,
                hasDependents: dependentTaskIds.length > 0,
                hasDependencies: dependsOnTaskIds.length > 0
            };
        });

        // Sort tasks by priority and due date
        return enhancedTasks.sort((a, b) => {
            // First sort by priority (higher priority first)
            const priorityDiff =
                this.taskPrioritizationService.getPriorityValue(b.priority) -
                this.taskPrioritizationService.getPriorityValue(a.priority);

            if (priorityDiff !== 0) {
                return priorityDiff;
            }

            // If priorities are equal, sort by due date (earlier due date first)
            // Tasks without due dates come last
            if (!a.dueDate && !b.dueDate) {
                return 0;
            }
            if (!a.dueDate) {
                return 1;
            }
            if (!b.dueDate) {
                return -1;
            }

            return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        });
    }

    /**
     * Gets a single task by its ID with dependency information
     * @param id The task ID
     * @param userId The user ID
     * @returns The task with dependency information
     */
    async getTaskById(id: string, userId: string): Promise<any> {
        const tenantId = this.tenantContextService.getTenantId();
        const cacheKey = CACHE_KEYS.TASK_BY_ID(tenantId, id, userId);
        let cachedTask: Task | undefined;
        try {
            cachedTask = await this.cacheService.get<Task>(cacheKey);
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskById] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${cacheKey} - Failed to get task from cache: ${err?.message}`,
                err
            );
        }
        if (cachedTask) {
            logger.debug(`[CACHE HIT] Task by ID: key=${cacheKey}`);
            const dependentTasks = await this.taskDependencyService.getDependentTasks(id);
            return this.addDependencyInfoToTask(cachedTask, dependentTasks);
        }

        // If not in cache, get from DB
        const task = await this.taskRepository.findOne({
            where: { id },
            relations: ['dependencies']
        });

        if (!task) {
            throw new NotFoundException(`Task with ID ${id} not found`);
        }

        const ttl = this.configService.get<number>('cache.ttl.taskDetails');
        try {
            await this.cacheService.set(cacheKey, task, ttl);
            logger.debug(`[CACHE SET] Task by ID: key=${cacheKey}`);
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskById] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${cacheKey} - Failed to set task in cache: ${err?.message}`,
                err
            );
        }

        // Get dependent tasks and return with dependency info
        const dependentTasks = await this.taskDependencyService.getDependentTasks(id);
        return this.addDependencyInfoToTask(task, dependentTasks);
    }

    /**
     * Updates an existing task
     * @param id The task ID
     * @param updateTaskDto The task update DTO
     * @param userId The ID of the user updating the task
     * @returns The updated task with dependency information
     */
    async updateTask(id: string, updateTaskDto: UpdateTaskDto, userId: string): Promise<any> {
        const tenantId = this.tenantContextService.getTenantId();
        const task = await this.getTaskById(id, userId);

        // Store old status for comparison
        const oldStatus = task.status;

        // Update task properties
        Object.assign(task, {
            ...updateTaskDto,
            updatedAt: new Date()
        });

        // If status is changing, validate the transition and update
        if (updateTaskDto.status && updateTaskDto.status !== oldStatus) {
            await this.validateStatusTransition(task.id, oldStatus, updateTaskDto.status);
            task.status = updateTaskDto.status;
        }

        // Save the updated task
        const updatedTask = await this.taskRepository.save(task);

        // If status changed, record in history
        if (updateTaskDto.status && updateTaskDto.status !== oldStatus) {
            await this.recordStatusChange(
                updatedTask.id,
                oldStatus,
                updatedTask.status,
                userId,
                undefined,
                { action: 'STATUS_CHANGED', updateTaskDto }
            );
        }

        // Get dependent tasks for the updated task
        const dependentTasks = await this.taskDependencyService.getDependentTasks(id);

        // Invalidate caches
        try {
            await this.cacheService.del(CACHE_KEYS.TASK_BY_ID(tenantId, id, userId));
            const stillExists = await this.cacheService.exists(
                CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)
            );
            logger.debug(
                `[CACHE DEL] Task by ID: key=${CACHE_KEYS.TASK_BY_ID(
                    tenantId,
                    id,
                    userId
                )} deleted=${!stillExists}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskUpdate] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)} - Failed to delete task cache: ${err?.message}`,
                err
            );
        }

        // Return the task with dependency information
        return this.addDependencyInfoToTask(updatedTask, dependentTasks);
    }

    /**
     * Changes the status of a task
     * @param id The task ID
     * @param statusChangeDto The status change DTO
     * @param userId The user ID
     * @param userName The user name
     * @returns The updated task with dependency information
     */
    async changeTaskStatus(
        id: string,
        statusChangeDto: TaskStatusChangeDto,
        userId: string,
        userName: string
    ): Promise<any> {
        const tenantId = this.tenantContextService.getTenantId();
        const task = await this.getTaskById(id, userId);
        const oldStatus = task.status;

        // If status is not changing, return the task as is
        if (statusChangeDto.status === oldStatus) {
            return task;
        }

        // Validate the status transition
        await this.validateStatusTransition(task.id, oldStatus, statusChangeDto.status);

        // Update the task status
        task.status = statusChangeDto.status;
        task.updatedAt = new Date();

        // Save the updated task
        const updatedTask = await this.taskRepository.save(task);

        // Record the status change in history
        await this.recordStatusChange(
            updatedTask.id,
            oldStatus,
            updatedTask.status,
            userId,
            userName,
            {
                action: 'STATUS_CHANGED',
                comment: statusChangeDto.comment
            }
        );

        // Get dependent tasks for the updated task
        const dependentTasks = await this.taskDependencyService.getDependentTasks(id);

        // Invalidate caches
        try {
            await this.cacheService.del(CACHE_KEYS.TASK_BY_ID(tenantId, id, userId));
            const stillExists = await this.cacheService.exists(
                CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)
            );
            logger.debug(
                `[CACHE DEL] Task by ID: key=${CACHE_KEYS.TASK_BY_ID(
                    tenantId,
                    id,
                    userId
                )} deleted=${!stillExists}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskStatusChange] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)} - Failed to delete task cache: ${err?.message}`,
                err
            );
        }

        // Return the task with dependency information
        return this.addDependencyInfoToTask(updatedTask, dependentTasks);
    }

    /**
     * Deletes a task
     * @param id The task ID
     * @param force Whether to force delete even if there are dependencies
     * @returns True if deleted successfully
     */
    async deleteTask(id: string, userId: string, force: boolean = false): Promise<boolean> {
        const tenantId = this.tenantContextService.getTenantId();

        // Get the task with its dependencies
        const task = await this.taskRepository.findOne({
            where: { id },
            relations: ['dependencies', 'dependencies.dependsOn']
        });

        if (!task) {
            throw new NotFoundException(`Task with ID ${id} not found`);
        }

        // Get dependent tasks (tasks that depend on this task)
        const dependentTasks = await this.taskDependencyService.getDependentTasks(id);

        // If there are dependent tasks and force is false, prevent deletion
        if (dependentTasks.length > 0 && !force) {
            throw new BadRequestException(
                `Cannot delete task with ${dependentTasks.length} dependent tasks. Use force=true to override.`
            );
        }

        // If force is true and there are dependent tasks, simply remove the dependencies
        if (dependentTasks.length > 0 && force) {
            logger.warn(`Force deleting task ${id} with ${dependentTasks.length} dependent tasks`);

            // Remove dependencies from dependent tasks
            await this.removeDependenciesFromDependentTasks(id, dependentTasks);
        }

        // If this task has dependencies, remove them
        if (task.dependencies && task.dependencies.length > 0) {
            await this.removeTaskDependencies(task.id, task.dependencies);
        }

        // Delete the task
        const result = await this.taskRepository.removeById(id);

        // Invalidate caches
        if (result) {
            try {
                await this.cacheService.del(CACHE_KEYS.TASK_BY_ID(tenantId, id, userId));
            } catch (err) {
                logger.warn(
                    `[CACHE][ERROR][TaskDelete] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)} - Failed to delete task cache: ${err?.message}`,
                    err
                );
            }
        }

        try {
            const stillExists = await this.cacheService.exists(
                CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)
            );
            logger.debug(
                `[CACHE DEL] Task by ID: key=${CACHE_KEYS.TASK_BY_ID(
                    tenantId,
                    id,
                    userId
                )} deleted=${!stillExists}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][TaskDelete] tenantId=${tenantId} userId=${userId} taskId=${id} cacheKey=${CACHE_KEYS.TASK_BY_ID(tenantId, id, userId)} - Failed to check existence after delete: ${err?.message}`,
                err
            );
        }

        return result;
    }

    /**
     * Removes dependencies from tasks that depend on the task being deleted
     * @param taskId The ID of the task being deleted
     * @param dependentTasks The list of dependencies on the task being deleted
     * @private
     */
    private async removeDependenciesFromDependentTasks(
        taskId: string,
        dependentTasks: TaskDependency[]
    ): Promise<void> {
        // For each dependent task, remove the dependency on the task being deleted
        for (const dependency of dependentTasks) {
            const dependentTaskId = dependency.task.id;

            // Remove the dependency
            await this.taskDependencyService.removeDependency(
                dependentTaskId,
                dependency.id,
                'SYSTEM'
            );

            // Log this change
            await this.recordStatusChange(
                dependentTaskId,
                dependency.task.status,
                dependency.task.status, // Status doesn't change
                'SYSTEM',
                undefined,
                {
                    action: 'DEPENDENCY_REMOVED',
                    message: `Dependency on task ${taskId} was removed because the task was deleted`
                }
            );
        }
    }

    /**
     * Removes all dependencies that a task has
     * @param taskId The task ID
     * @param dependencies The list of dependencies to remove
     * @private
     */
    private async removeTaskDependencies(
        taskId: string,
        dependencies: TaskDependency[]
    ): Promise<void> {
        for (const dependency of dependencies) {
            await this.taskDependencyService.removeDependency(taskId, dependency.id, 'SYSTEM');
        }
    }

    /**
     * Adds dependency information to a task object
     * @param task The task object
     * @param dependentTasks The list of tasks that depend on this task
     * @returns The task object with added dependency information
     * @private
     */
    private addDependencyInfoToTask(task: Task, dependentTasks: TaskDependency[]): any {
        // Get the IDs of tasks that depend on this task
        const dependentTaskIds = dependentTasks.map((dep) => dep.task.id);

        // Get the IDs of tasks that this task depends on
        const dependsOnTaskIds = (task.dependencies || []).map((dep) => dep.dependsOnId);

        // Create a new object without the entity's dependencies and dependents fields
        const { ...taskWithoutDependencies } = task;

        // Return enhanced task with dependency information
        return {
            ...taskWithoutDependencies,
            dependentTasks: dependentTaskIds,
            dependsOnTasks: dependsOnTaskIds,
            hasDependents: dependentTaskIds.length > 0,
            hasDependencies: dependsOnTaskIds.length > 0
        };
    }

    /**
     * Records a status change in the task's history
     * @param taskId The task ID
     * @param fromStatus The original status
     * @param toStatus The new status
     * @param changedBy The user ID of the person who made the change
     * @param changedByName The name of the person who made the change
     * @param metadata Additional metadata for the history record
     * @private
     */
    private async recordStatusChange(
        taskId: string,
        fromStatus: TaskStatus,
        toStatus: TaskStatus,
        changedBy: string,
        changedByName?: string,
        metadata?: any
    ): Promise<void> {
        await this.taskHistoryRepository.save({
            taskId,
            fromStatus,
            toStatus,
            changedBy,
            changedByName,
            metadata
        });
    }

    /**
     * Validates if a status transition is allowed
     * @param taskId The task ID
     * @param fromStatus The original status
     * @param toStatus The new status
     * @private
     */
    private async validateStatusTransition(
        taskId: string,
        fromStatus: TaskStatus,
        toStatus: TaskStatus
    ): Promise<void> {
        // Define valid transitions
        const validTransitions: Record<TaskStatus, TaskStatus[]> = {
            [TaskStatus.OPEN]: [TaskStatus.IN_PROGRESS, TaskStatus.BLOCKED],
            [TaskStatus.IN_PROGRESS]: [TaskStatus.DONE, TaskStatus.BLOCKED],
            [TaskStatus.BLOCKED]: [TaskStatus.OPEN],
            [TaskStatus.DONE]: [TaskStatus.OPEN] // Allow reopening if needed
        };

        // Check if the transition is valid
        if (!validTransitions[fromStatus].includes(toStatus)) {
            throw new BadRequestException(
                `Invalid status transition from ${fromStatus} to ${toStatus}`
            );
        }

        // Additional validation for specific transitions
        if (toStatus === TaskStatus.IN_PROGRESS) {
            // Check if all dependencies are completed
            const allDependenciesCompleted =
                await this.taskDependencyService.areAllDependenciesCompleted(taskId);
            if (!allDependenciesCompleted) {
                throw new ConflictException(
                    'Cannot start task until all dependencies are completed'
                );
            }
        } else if (toStatus === TaskStatus.DONE) {
            // Check if there are incomplete dependent tasks
            const hasIncompleteDependentTasks =
                await this.taskDependencyService.hasIncompleteDependentTasks(taskId);
            if (hasIncompleteDependentTasks) {
                throw new ConflictException(
                    'Cannot complete task while there are incomplete dependent tasks'
                );
            }
        }
    }
}
