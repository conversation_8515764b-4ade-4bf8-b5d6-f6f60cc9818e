import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { appConfig, serviceConfig } from '@app/common';
import { ValidationPipe } from '@nestjs/common';
import helmet from 'helmet';
import { WinstonModule } from 'nest-winston';
import { instance } from '@app/common/utils/logger.util';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const app = await NestFactory.create(AppModule, {
        logger: WinstonModule.createLogger({
            instance: instance
        })
    });

    // app.setGlobalPrefix(appConfig().apiGlobalPrefix);
    app.use(helmet());
    app.enableCors({
        origin: appConfig().corsOrigin,
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true
    });

    // Set up global validation pipe
    app.useGlobalPipes(
        new ValidationPipe({
            transform: true,
            forbidNonWhitelisted: true,
            whitelist: true,
            stopAtFirstError: true,
            transformOptions: {
                enableImplicitConversion: true
            }
        })
    );

    // Start the application
    await app.listen(serviceConfig().core.port);
    Logger.log(`Core application running on ${await app.getUrl()}`);
}
bootstrap();
