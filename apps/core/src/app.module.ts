import { Module } from '@nestjs/common';
import { CommonModule } from '@app/common';
import { ApiResponseModule } from '@app/common/api-response';
import { ExampleController } from './example/example.controller';
import { HealthModule } from './health/health.module';
import { HttpModule } from '@nestjs/axios';
import { ProxyController } from './proxy/proxy.controller';
import { ProxyService } from './proxy/proxy.service';

const apiResponseExcludedPaths = [
    '/api/health',
    '/api/health/db',
    '/api/health/all',
    '/health',
    '/ping',
    '/metrics'
];

@Module({
    imports: [
        CommonModule,
        HealthModule,
        HttpModule,
        ApiResponseModule.forRoot({
            excludePaths: apiResponseExcludedPaths
        })
    ],
    controllers: [ExampleController, ProxyController],
    providers: [ProxyService]
})
export class AppModule {}
