import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

interface ServiceEndpoint {
    host: string;
    port: number;
    prefix: string;
}

@Injectable()
export class ProxyService {
    private readonly services: Map<string, ServiceEndpoint> = new Map();

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService
    ) {
        this.initializeServices();
    }

    private initializeServices(): void {
        const serviceConfig = this.configService.get('service');
        const isDocker = process.env.NODE_ENV === 'production' || process.env.DOCKER_ENV === 'true';

        // Map service prefixes to their endpoints
        this.services.set('auth', {
            host: isDocker ? 'tk-lpm-auth' : 'localhost',
            port: serviceConfig.auth.port,
            prefix: serviceConfig.auth.prefix
        });

        this.services.set('document-engine', {
            host: isDocker ? 'tk-lpm-document-engine' : 'localhost',
            port: serviceConfig.documentEngine.port,
            prefix: serviceConfig.documentEngine.prefix
        });

        this.services.set('communication', {
            host: isDocker ? 'tk-lpm-communication' : 'localhost',
            port: serviceConfig.communication.port,
            prefix: serviceConfig.communication.prefix
        });

        this.services.set('case-management', {
            host: isDocker ? 'tk-lpm-case-management' : 'localhost',
            port: serviceConfig.caseManagement.port,
            prefix: serviceConfig.caseManagement.prefix
        });

        this.services.set('task-management', {
            host: isDocker ? 'tk-lpm-task-management' : 'localhost',
            port: serviceConfig.taskManagement.port,
            prefix: serviceConfig.taskManagement?.prefix || '/api/task-management'
        });
    }

    async proxyRequest(
        serviceName: string,
        path: string,
        method: string,
        body?: any,
        headers?: any,
        query?: any
    ): Promise<{ data: any; headers: any }> {
        const service = this.services.get(serviceName);

        if (!service) {
            throw new HttpException(`Service '${serviceName}' not found`, HttpStatus.NOT_FOUND);
        }

        // Build the target URL
        const baseUrl = `http://${service.host}:${service.port}`;
        const targetPath = path.startsWith('/') ? path : `/${path}`;
        const url = `${baseUrl}${targetPath}`;

        // Prepare headers (exclude host and content-length to avoid conflicts)
        const forwardHeaders = { ...headers };
        delete forwardHeaders.host;
        delete forwardHeaders['content-length'];

        try {
            let response;
            const config = {
                headers: forwardHeaders,
                params: query,
                timeout: 30000 // 30 seconds timeout
            };

            switch (method.toUpperCase()) {
                case 'GET':
                    response = await firstValueFrom(this.httpService.get(url, config));
                    break;
                case 'POST':
                    response = await firstValueFrom(this.httpService.post(url, body, config));
                    break;
                case 'PUT':
                    response = await firstValueFrom(this.httpService.put(url, body, config));
                    break;
                case 'PATCH':
                    response = await firstValueFrom(this.httpService.patch(url, body, config));
                    break;
                case 'DELETE':
                    response = await firstValueFrom(this.httpService.delete(url, config));
                    break;
                default:
                    throw new HttpException(
                        `Method ${method} not supported`,
                        HttpStatus.METHOD_NOT_ALLOWED
                    );
            }

            return { data: response.data, headers: response.headers };
        } catch (error) {
            // Handle axios errors and forward appropriate status codes
            if (error.response) {
                throw new HttpException(
                    error.response.data || error.message,
                    error.response.status
                );
            }

            // Handle network errors
            throw new HttpException(
                `Service '${serviceName}' is unavailable`,
                HttpStatus.SERVICE_UNAVAILABLE
            );
        }
    }

    getServiceFromPath(path: string): { serviceName: string; remainingPath: string } | null {
        // Remove leading /api if present
        const cleanPath = path.replace(/^\/api\//, '');

        // Extract the service name from the path
        for (const [serviceName] of this.services) {
            if (cleanPath.startsWith(serviceName)) {
                const remainingPath = cleanPath.substring(serviceName.length);
                return {
                    serviceName,
                    remainingPath: remainingPath.startsWith('/')
                        ? remainingPath
                        : `/${remainingPath}`
                };
            }
        }

        return null;
    }
}
