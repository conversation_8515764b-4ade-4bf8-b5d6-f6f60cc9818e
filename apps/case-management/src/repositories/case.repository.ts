import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Repository } from 'typeorm';
import { CaseFilterDto } from '../dto/case-filter.dto';
import { Case } from '@app/common/typeorm/entities';

@Injectable()
export class CaseRepository extends BaseTenantRepository<Case> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Case, tenantContextService, tenantConnectionService);
    }

    async findByCaseNumber(caseNumber: string): Promise<Case | null> {
        return this.findOne({ where: { caseNumber } });
    }

    async findByCaseId(id: string): Promise<Case | null> {
        return this.findOne({ where: { id } });
    }

    async findWithFilters(filterDto: CaseFilterDto): Promise<[Case[], number]> {
        const {
            page = 1,
            limit = 10,
            sortBy = 'createdAt',
            order = 'DESC',
            search,
            ...filters
        } = filterDto;

        const repository = await this.getTenantRepository();
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client');

        // Apply filters
        if (filters.status) {
            queryBuilder.andWhere('case.status = :status', { status: filters.status });
        }

        if (filters.priority) {
            queryBuilder.andWhere('case.priority = :priority', { priority: filters.priority });
        }

        if (filters.type) {
            queryBuilder.andWhere('case.type = :type', { type: filters.type });
        }

        if (filters.clientId) {
            queryBuilder.andWhere('case.clientId = :clientId', { clientId: filters.clientId });
        }

        if (filters.createdAfter) {
            queryBuilder.andWhere('case.createdAt >= :createdAfter', {
                createdAfter: new Date(filters.createdAfter)
            });
        }

        if (filters.createdBefore) {
            queryBuilder.andWhere('case.createdAt <= :createdBefore', {
                createdBefore: new Date(filters.createdBefore)
            });
        }

        if (filters.deadlineAfter) {
            queryBuilder.andWhere('case.deadline >= :deadlineAfter', {
                deadlineAfter: new Date(filters.deadlineAfter)
            });
        }

        if (filters.deadlineBefore) {
            queryBuilder.andWhere('case.deadline <= :deadlineBefore', {
                deadlineBefore: new Date(filters.deadlineBefore)
            });
        }

        if (filters.assignedTo) {
            queryBuilder
                .innerJoin('case_assignments', 'assignment', 'case.id = assignment.case_id')
                .andWhere('assignment.user_id = :userId', { userId: filters.assignedTo })
                .andWhere('assignment.is_active = true');
        }

        // Apply search with advanced functionality
        if (search) {
            queryBuilder.andWhere(
                '(' +
                    'case.title ILIKE :search OR ' +
                    'case.description ILIKE :search OR ' +
                    'case.caseNumber ILIKE :search OR ' +
                    'client.name ILIKE :search OR ' +
                    'client.email ILIKE :search' +
                    ')',
                { search: `%${search}%` }
            );
        }

        // Apply sorting
        if (sortBy.includes('.')) {
            // Handle sorting by related entity fields
            const [relation, field] = sortBy.split('.');
            queryBuilder.orderBy(`${relation}.${field}`, order);
        } else {
            queryBuilder.orderBy(`case.${sortBy}`, order);
        }

        // Apply pagination
        queryBuilder.skip((page - 1) * limit).take(limit);

        // Execute query
        const [cases, total] = await queryBuilder.getManyAndCount();

        return [cases, total];
    }

    /**
     * Performs a fast search for cases
     * Optimized for quick response time (<1ms)
     */
    async quickSearch(searchTerm: string, limit: number = 10): Promise<Case[]> {
        if (!searchTerm || searchTerm.length < 2) {
            return [];
        }

        const repository = await this.getTenantRepository();

        // Use a simplified query for speed
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoin('case.client', 'client')
            .select([
                'case.id',
                'case.caseNumber',
                'case.title',
                'case.status',
                'case.priority',
                'case.type',
                'case.createdAt',
                'client.id',
                'client.name'
            ])
            .where(
                '(' +
                    'case.caseNumber ILIKE :search OR ' +
                    'case.title ILIKE :search OR ' +
                    'client.name ILIKE :search' +
                    ')',
                { search: `%${searchTerm}%` }
            )
            .orderBy('case.createdAt', 'DESC')
            .take(limit);

        return queryBuilder.getMany();
    }

    async findByAssignedUser(userId: string): Promise<Case[]> {
        const repository = await this.getTenantRepository();

        // Using a custom query to find cases assigned to a specific user
        const cases = await repository
            .createQueryBuilder('case')
            .innerJoin('case_assignments', 'assignment', 'case.id = assignment.case_id')
            .where('assignment.user_id = :userId', { userId })
            .andWhere('assignment.is_active = true')
            .getMany();

        return cases;
    }

    /**
     * Gets the tenant repository - public method to allow access from services
     */
    async getRepository(): Promise<Repository<Case>> {
        return this.getTenantRepository();
    }

    /**
     * Searches for documents (attachments) across all cases
     */
    async searchDocuments(searchTerm: string, limit: number = 10): Promise<any[]> {
        const repository = await this.getTenantRepository();

        // Use a query to find attachments matching the search term
        const queryBuilder = repository
            .createQueryBuilder('case')
            .innerJoin('case_attachments', 'attachment', 'case.id = attachment.case_id')
            .select([
                'case.id as caseId',
                'case.caseNumber as caseNumber',
                'case.title as caseTitle',
                'attachment.id as id',
                'attachment.filename as filename',
                'attachment.url as url',
                'attachment.uploaded_at as uploadedAt'
            ])
            .where('attachment.filename ILIKE :search', { search: `%${searchTerm}%` })
            .orderBy('attachment.uploaded_at', 'DESC')
            .limit(limit);

        return queryBuilder.getRawMany();
    }
}
