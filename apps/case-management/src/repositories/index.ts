import { CaseRepository } from './case.repository';
import { ClientRepository } from './client.repository';
import { CaseAssignmentRepository } from './case-assignment.repository';
import { CaseNoteRepository } from './case-note.repository';
import { CaseAttachmentRepository } from './case-attachment.repository';
import { CaseAuditRepository } from './case-audit.repository';
import { CaseContactRepository } from './case-contact.repository';
import { CaseEventRepository } from './case-event.repository';
import { CaseRelationRepository } from './case-relation.repository';

export const CASE_MANAGEMENT_REPOSITORIES = [
    CaseRepository,
    ClientRepository,
    CaseAssignmentRepository,
    CaseNoteRepository,
    CaseAttachmentRepository,
    CaseAuditRepository,
    CaseContactRepository,
    CaseEventRepository,
    CaseRelationRepository
];

export {
    CaseRepository,
    ClientRepository,
    CaseAssignmentRepository,
    CaseNoteRepository,
    CaseAttachmentRepository,
    CaseAuditRepository,
    CaseContactRepository,
    CaseEventRepository,
    CaseRelationRepository
};
