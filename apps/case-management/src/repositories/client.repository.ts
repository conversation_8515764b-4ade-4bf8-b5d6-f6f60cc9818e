import { Injectable } from '@nestjs/common';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';
import { Case } from '@app/common/typeorm/entities/tenant/case.entity';
import { FindManyOptions, ILike, Repository } from 'typeorm';
import { Client } from '@app/common/typeorm/entities/tenant/client.entity';

@Injectable()
export class ClientRepository extends BaseTenantRepository<Client> {
    constructor(
        protected readonly tenantContextService: TenantContextService,
        protected readonly tenantConnectionService: TenantConnectionService
    ) {
        super(Client, tenantContextService, tenantConnectionService);
    }

    async findByName(name: string): Promise<Client[]> {
        return this.find({
            where: {
                name: ILike(`%${name}%`)
            }
        });
    }

    async findByEmail(email: string): Promise<Client | null> {
        return this.findOne({
            where: {
                email
            }
        });
    }

    async findWithPagination(
        page: number = 1,
        limit: number = 10,
        search?: string
    ): Promise<[Client[], number]> {
        const skip = (page - 1) * limit;

        const options: FindManyOptions<Client> = {
            skip,
            take: limit,
            order: {
                name: 'ASC'
            }
        };

        if (search) {
            options.where = [{ name: ILike(`%${search}%`) }, { email: ILike(`%${search}%`) }];
        }

        const repository = await this.getTenantRepository();
        return repository.findAndCount(options);
    }

    /**
     * Gets the tenant repository - public method to allow access from services
     */
    async getRepository(): Promise<Repository<Client>> {
        return this.getTenantRepository();
    }

    /**
     * Finds cases associated with a client
     * @param clientId The client ID
     * @returns List of cases for the client
     */
    async findClientCases(clientId: string): Promise<Case[]> {
        const repository = await this.getTenantRepository();

        // Use a query to find cases for the client
        const queryBuilder = repository
            .createQueryBuilder('client')
            .innerJoin('cases', 'case', 'client.id = case.client_id')
            .select([
                'case.id as id',
                'case.caseNumber as caseNumber',
                'case.title as title',
                'case.status as status',
                'case.priority as priority',
                'case.type as type',
                'case.createdAt as createdAt'
            ])
            .where('client.id = :clientId', { clientId })
            .orderBy('case.createdAt', 'DESC');

        return queryBuilder.getRawMany();
    }
}
