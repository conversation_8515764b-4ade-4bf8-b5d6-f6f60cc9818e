import { Injectable } from '@nestjs/common';

/**
 * Interface for pagination metadata
 */
export interface PaginationMeta {
    page: number;
    limit: number;
    total: number;
    pages: number;
}

/**
 * Interface for paginated response
 */
export interface PaginatedResponse<T> {
    data: T[];
    meta: {
        pagination: PaginationMeta;
    };
}

/**
 * Service for handling pagination
 */
@Injectable()
export class PaginationService {
    /**
     * Creates a paginated response
     * @param data The data to paginate
     * @param total The total number of items
     * @param page The current page
     * @param limit The number of items per page
     * @returns A paginated response
     */
    createPaginatedResponse<T>(
        data: T[],
        total: number,
        page: number,
        limit: number
    ): PaginatedResponse<T> {
        const pages = Math.ceil(total / limit);

        return {
            data,
            meta: {
                pagination: {
                    page,
                    limit,
                    total,
                    pages
                }
            }
        };
    }

    /**
     * Calculates the skip value for pagination
     * @param page The current page
     * @param limit The number of items per page
     * @returns The number of items to skip
     */
    calculateSkip(page: number, limit: number): number {
        return (page - 1) * limit;
    }

    /**
     * Validates and normalizes pagination parameters
     * @param page The current page
     * @param limit The number of items per page
     * @returns Normalized pagination parameters
     */
    normalizePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
        const normalizedPage = page && page > 0 ? page : 1;
        const normalizedLimit = limit && limit > 0 ? Math.min(limit, 100) : 10;

        return {
            page: normalizedPage,
            limit: normalizedLimit
        };
    }
}
