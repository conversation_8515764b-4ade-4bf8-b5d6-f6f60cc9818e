import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CaseNoteRepository } from '../repositories/case-note.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CreateNoteDto } from '../dto/create-note.dto';
import { CaseAuditService } from './case-audit.service';
import { Request } from 'express';
import { CaseNote } from '@app/common/typeorm/entities';

@Injectable()
export class CaseNoteService {
    private readonly logger = new Logger(CaseNoteService.name);

    constructor(
        private readonly caseNoteRepository: CaseNoteRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Creates a new note for a case
     */
    async createNote(
        caseId: string,
        createNoteDto: CreateNoteDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CaseNote> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create note
        const note = await this.caseNoteRepository.create({
            caseId,
            content: createNoteDto.content,
            createdBy,
            createdByName,
            isPinned: createNoteDto.isPinned || false,
            isPrivate: createNoteDto.isPrivate || false
        });

        const savedNote = await this.caseNoteRepository.save(note);

        // Log note creation
        await this.caseAuditService.logNoteAdded(
            caseId,
            createdBy,
            createdByName,
            request,
            savedNote.id
        );

        return savedNote;
    }

    /**
     * Gets all notes for a case
     */
    async getCaseNotes(caseId: string, includePrivate: boolean = false): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findByCaseId(caseId, includePrivate);
    }

    /**
     * Gets a specific note by ID
     */
    async getNoteById(noteId: string): Promise<CaseNote> {
        const note = await this.caseNoteRepository.findOne({
            where: { id: noteId }
        });

        if (!note) {
            throw new NotFoundException(`Note with ID ${noteId} not found`);
        }

        return note;
    }

    /**
     * Toggles the pin status of a note
     */
    async togglePinStatus(noteId: string, isPinned: boolean): Promise<void> {
        const note = await this.getNoteById(noteId);

        if (note.isPinned === isPinned) {
            return; // No change needed
        }

        await this.caseNoteRepository.togglePinStatus(noteId, isPinned);
    }

    /**
     * Gets all pinned notes for a case
     */
    async getPinnedNotes(caseId: string): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findPinnedNotes(caseId);
    }

    /**
     * Gets the most recent notes for a case
     * @param caseId The case ID
     * @param limit The maximum number of notes to return
     * @returns The most recent notes
     */
    async getRecentNotes(caseId: string, limit: number = 3): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findRecentNotes(caseId, limit);
    }
}
