import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@app/common';
import { HealthModule } from './health/health.module';
import { ApiResponseModule } from '@app/common/api-response';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CASE_MANAGEMENT_CONTROLLERS } from './controllers';
import { CASE_MANAGEMENT_SERVICES } from './services';
import { CASE_MANAGEMENT_REPOSITORIES } from './repositories';
import { CaseAssignmentService } from './services/case-assignment.service';
import { CaseAuditService } from './services/case-audit.service';
import { CASE_ASSIGNMENT_CHECKER } from '@app/common/interfaces/case-assignment-checker.interface';
import { CASE_AUDIT_SERVICE } from '@app/common';
import { AuthModule } from '@app/common/auth/auth.module';
import {
    Case,
    CaseAssignment,
    CaseAttachment,
    CaseAudit,
    CaseNote,
    Client
} from '@app/common/typeorm/entities';
import { TenantContextMiddleware } from './middleware/tenant-context.middleware';

const apiResponseExcludedPaths = [
    '/api/case-management/health',
    '/api/case-management/health/db',
    '/health',
    '/ping'
];

@Module({
    imports: [
        CommonModule,
        ConfigModule.forRoot({
            isGlobal: true
        }),
        ApiResponseModule.forRoot({
            excludePaths: apiResponseExcludedPaths
        }),
        TypeOrmModule.forFeature([
            Case,
            Client,
            CaseAssignment,
            CaseNote,
            CaseAttachment,
            CaseAudit
        ]),
        HealthModule,
        AuthModule // Import the AuthModule from the common library
    ],
    controllers: [...CASE_MANAGEMENT_CONTROLLERS],
    providers: [
        ...CASE_MANAGEMENT_SERVICES,
        ...CASE_MANAGEMENT_REPOSITORIES,
        {
            provide: CASE_ASSIGNMENT_CHECKER,
            useExisting: CaseAssignmentService
        },
        {
            provide: CASE_AUDIT_SERVICE,
            useExisting: CaseAuditService
        }
    ]
})
export class AppModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(TenantContextMiddleware).forRoutes('*'); // Apply to all routes
    }
}
