import { IsEnum, IsIn, IsOptional, IsString, IsUUID } from 'class-validator';

import { Type } from 'class-transformer';
import { CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';

export class CaseFilterDto {
    @IsOptional()
    @Type(() => Number)
    page?: number = 1;

    @IsOptional()
    @Type(() => Number)
    limit?: number = 10;

    @IsOptional()
    @IsString()
    sortBy?: string = 'createdAt';

    @IsOptional()
    @IsIn(['ASC', 'DESC'])
    order?: 'ASC' | 'DESC' = 'DESC';

    @IsOptional()
    @IsString()
    search?: string;

    @IsOptional()
    @IsEnum(CaseStatus)
    status?: CaseStatus;

    @IsOptional()
    @IsEnum(CasePriority)
    priority?: CasePriority;

    @IsOptional()
    @IsEnum(CaseType)
    type?: CaseType;

    @IsOptional()
    @IsUUID()
    clientId?: string;

    @IsOptional()
    @IsUUID()
    assignedTo?: string;

    @IsOptional()
    @IsString()
    createdAfter?: string;

    @IsOptional()
    @IsString()
    createdBefore?: string;

    @IsOptional()
    @IsString()
    deadlineAfter?: string;

    @IsOptional()
    @IsString()
    deadlineBefore?: string;
}
