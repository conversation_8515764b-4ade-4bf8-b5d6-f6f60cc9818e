import {
    <PERSON>,
    Post,
    Body,
    Param,
    UseGuards,
    Request,
    Get,
    BadRequestException,
    Ip
} from '@nestjs/common';
import { CaseStateMachineService } from '@app/common/cases/case-state-machine.service';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { AppRole } from '@app/common/enums/roles.enums';
import { Roles } from '@app/common/roles/decorators';
import { CaseTransitionDto } from '../dto/case-transition.dto';
import { CaseRepository } from '../repositories';
import { TenantGuard } from '@app/common/multi-tenancy/tenant.guard';

@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseStateController {
    constructor(
        private readonly caseStateMachineService: CaseStateMachineService,
        private readonly caseManagementRepository: CaseRepository
    ) {}

    @Post(':id/transition')
    @Roles(AppRole.USER, AppRole.ADMIN, AppRole.SUPERVISOR, AppRole.MANAGER)
    async transitionState(
        @Param('id') id: string,
        @Body() transitionDto: CaseTransitionDto,
        @Request() req,
        @Ip() ipAddress: string
    ) {
        // Prepare the data object with consistent format
        const data = {
            ...transitionDto?.data,
            notes: transitionDto?.notes,
            holdReason: transitionDto?.holdReason,
            reopenReason: transitionDto?.reopenReason,
            assignments: transitionDto?.assignments
        };

        //Temporary fix for user id
        req.user.id = req.user.id || req.user.systemUserId;
        // The RolesGuard has already verified the user has appropriate roles
        return this.caseStateMachineService.transition(
            id,
            transitionDto.targetState,
            req.user,
            data,
            ipAddress
        );
    }

    @Get(':id/valid-transitions')
    @Roles(AppRole.USER, AppRole.ADMIN, AppRole.SUPERVISOR, AppRole.MANAGER, AppRole.LAWYER)
    async getValidTransitions(@Param('id') id: string, @Request() req) {
        const caseEntity = await this.caseManagementRepository.findByCaseId(id);

        if (!caseEntity) {
            throw new BadRequestException(`Case with ID ${id} not found`);
        }

        // Get available transitions based on case state and user
        const availablePermissions = req.user.roles;
        return {
            currentState: caseEntity.status,
            allowedPermissions: availablePermissions
        };
    }
}
