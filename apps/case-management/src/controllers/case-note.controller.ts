import { Body, Controller, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseNoteService } from '../services/case-note.service';
import { CreateNoteDto } from '../dto/create-note.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { IsLawyer } from '@app/common/roles/decorators';
import { Request } from 'express';

@Controller('cases/:caseId/notes')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseNoteController {
    constructor(private readonly caseNoteService: CaseNoteService) {}

    /**
     * Create a new note for a case
     */
    @Post()
    @IsLawyer()
    async createNote(
        @Param('caseId') caseId: string,
        @Body() createNoteDto: CreateNoteDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const note = await this.caseNoteService.createNote(
            caseId,
            createNoteDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(note, 'Note created successfully');
    }

    /**
     * Get all notes for a case
     */
    @Get()
    @IsLawyer()
    async getCaseNotes(
        @Param('caseId') caseId: string,
        @Query('includePrivate') includePrivate: boolean = false
    ) {
        const notes = await this.caseNoteService.getCaseNotes(caseId, includePrivate);
        return ApiResponseUtil.ok(notes, 'Notes retrieved successfully');
    }

    /**
     * Get a specific note by ID
     */
    @Get(':noteId')
    @IsLawyer()
    async getNoteById(@Param('noteId') noteId: string) {
        const note = await this.caseNoteService.getNoteById(noteId);
        return ApiResponseUtil.ok(note, 'Note retrieved successfully');
    }

    /**
     * Toggle the pin status of a note
     */
    @Patch(':noteId/pin')
    @IsLawyer()
    async togglePinStatus(@Param('noteId') noteId: string, @Body('isPinned') isPinned: boolean) {
        await this.caseNoteService.togglePinStatus(noteId, isPinned);
        return ApiResponseUtil.ok(null, `Note ${isPinned ? 'pinned' : 'unpinned'} successfully`);
    }

    /**
     * Get all pinned notes for a case
     * Returns important notes flagged as priority
     */
    @Get('pinned')
    @IsLawyer()
    async getPinnedNotes(@Param('caseId') caseId: string) {
        const notes = await this.caseNoteService.getPinnedNotes(caseId);
        return ApiResponseUtil.ok(notes, 'Pinned notes retrieved successfully');
    }

    /**
     * Get recent notes for a case
     * Returns the 3 most recent notes
     */
    @Get('recent')
    @IsLawyer()
    async getRecentNotes(@Param('caseId') caseId: string) {
        const notes = await this.caseNoteService.getRecentNotes(caseId, 3);
        return ApiResponseUtil.ok(notes, 'Recent notes retrieved successfully');
    }
}
