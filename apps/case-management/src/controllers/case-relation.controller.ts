import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { CaseRelationService } from '../services/case-relation.service';
import { CreateCaseRelationDto } from '../dto/create-case-relation.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { IsLawyer } from '@app/common/roles/decorators';
import { Request } from 'express';

@Controller('cases/:caseId/related')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseRelationController {
    constructor(private readonly caseRelationService: CaseRelationService) {}

    /**
     * Create a new relation between two cases
     */
    @Post()
    @IsLawyer()
    async createRelation(
        @Param('caseId') caseId: string,
        @Body() createCaseRelationDto: CreateCaseRelationDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const relation = await this.caseRelationService.createRelation(
            caseId,
            createCaseRelationDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(relation, 'Case relation created successfully');
    }

    /**
     * Get all relations for a case
     */
    @Get()
    @IsLawyer()
    async getRelations(@Param('caseId') caseId: string) {
        const relations = await this.caseRelationService.getCaseRelations(caseId);
        return ApiResponseUtil.ok(relations, 'Case relations retrieved successfully');
    }

    /**
     * Delete a relation
     */
    @Delete(':relationId')
    @IsLawyer()
    async deleteRelation(
        @Param('caseId') caseId: string,
        @Param('relationId') relationId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseRelationService.deleteRelation(
            caseId,
            relationId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case relation deleted successfully');
    }
}
