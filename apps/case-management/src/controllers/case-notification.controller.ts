import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseNotificationService } from '../services/case-notification.service';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { IsLawyer } from '@app/common/roles/decorators';
import { Request } from 'express';

@Controller('cases/notifications')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseNotificationController {
    constructor(private readonly caseNotificationService: CaseNotificationService) {}

    /**
     * Get cases with upcoming deadlines
     */
    @Get('upcoming-deadlines')
    @IsLawyer()
    async getUpcomingDeadlines(@Query('hours') hours: number = 24) {
        const cases = await this.caseNotificationService.checkUpcomingDeadlines(hours);
        return ApiResponseUtil.ok(
            cases,
            `Cases with deadlines in the next ${hours} hours retrieved successfully`
        );
    }

    /**
     * Get cases with missed deadlines
     */
    @Get('missed-deadlines')
    @IsLawyer()
    async getMissedDeadlines() {
        const cases = await this.caseNotificationService.checkMissedDeadlines();
        return ApiResponseUtil.ok(cases, 'Cases with missed deadlines retrieved successfully');
    }

    /**
     * Set a custom reminder for a case deadline
     */
    @Post(':caseId/reminders')
    @IsLawyer()
    async setCustomReminder(
        @Param('caseId') caseId: string,
        @Body('reminderTime') reminderTime: string,
        @Body('reminderType') reminderType: string = 'email',
        @Req() request: Request
    ) {
        const user = request['user'];
        const reminder = await this.caseNotificationService.setCustomReminder(
            caseId,
            user.systemUserId,
            user.preferred_username || user.email,
            reminderTime,
            reminderType
        );

        return ApiResponseUtil.created(reminder, 'Custom reminder set successfully');
    }
}
