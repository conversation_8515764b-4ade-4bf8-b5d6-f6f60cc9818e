import { Body, Controller, Delete, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { CaseAttachmentService } from '../services/case-attachment.service';
import { CreateAttachmentDto } from '../dto/create-attachment.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { IsLawyer } from '@app/common/roles/decorators';
import { Request } from 'express';
import { DocumentType } from '@app/common/typeorm/entities/tenant/case-attachment.entity';

@Controller('cases/:caseId/attachments')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseAttachmentController {
    constructor(private readonly caseAttachmentService: CaseAttachmentService) {}

    /**
     * Create a new attachment for a case
     */
    @Post()
    @IsLawyer()
    async createAttachment(
        @Param('caseId') caseId: string,
        @Body() createAttachmentDto: CreateAttachmentDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const attachment = await this.caseAttachmentService.createAttachment(
            caseId,
            createAttachmentDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(attachment, 'Attachment created successfully');
    }

    /**
     * Get all attachments for a case
     */
    @Get()
    @IsLawyer()
    async getCaseAttachments(@Param('caseId') caseId: string) {
        const attachments = await this.caseAttachmentService.getCaseAttachments(caseId);
        return ApiResponseUtil.ok(attachments, 'Attachments retrieved successfully');
    }

    /**
     * Get a specific attachment by ID
     */
    @Get(':attachmentId')
    @IsLawyer()
    async getAttachmentById(@Param('attachmentId') attachmentId: string) {
        const attachment = await this.caseAttachmentService.getAttachmentById(attachmentId);
        return ApiResponseUtil.ok(attachment, 'Attachment retrieved successfully');
    }

    /**
     * Delete an attachment
     */
    @Delete(':attachmentId')
    @IsLawyer()
    async deleteAttachment(@Param('attachmentId') attachmentId: string, @Req() request: Request) {
        const user = request['user'];
        await this.caseAttachmentService.deleteAttachment(
            attachmentId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Attachment deleted successfully');
    }

    /**
     * Search attachments by filename
     */
    @Get('search')
    @IsLawyer()
    async searchAttachmentsByFilename(
        @Param('caseId') caseId: string,
        @Query('filename') filename: string
    ) {
        const attachments = await this.caseAttachmentService.searchAttachmentsByFilename(
            caseId,
            filename
        );

        return ApiResponseUtil.ok(attachments, 'Attachments retrieved successfully');
    }

    /**
     * Get attachments by document type
     */
    @Get('type/:documentType')
    @IsLawyer()
    async getAttachmentsByDocumentType(
        @Param('caseId') caseId: string,
        @Param('documentType') documentType: DocumentType
    ) {
        const attachments = await this.caseAttachmentService.getAttachmentsByDocumentType(
            caseId,
            documentType
        );

        return ApiResponseUtil.ok(
            attachments,
            `Attachments of type ${documentType} retrieved successfully`
        );
    }
}
