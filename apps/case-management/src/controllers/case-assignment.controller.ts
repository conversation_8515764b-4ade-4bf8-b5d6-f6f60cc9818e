import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { CaseAssignmentService } from '../services/case-assignment.service';
import { AssignCaseDto } from '../dto/assign-case.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { Request } from 'express';
import { RolesGuard } from '@app/common/guards/roles.guard';

@Controller('cases/:caseId/assignments')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseAssignmentController {
    constructor(private readonly caseAssignmentService: CaseAssignmentService) {}

    /**
     * Assign a case to a user
     * Only Super Admin can assign cases
     */
    @Post()
    async assignCase(
        @Param('caseId') caseId: string,
        @Body() assignCaseDto: AssignCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const assignment = await this.caseAssignmentService.assignCase(
            caseId,
            assignCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(assignment, 'Case assigned successfully');
    }

    /**
     * Unassign a user from a case
     * Only Super Admin can unassign cases
     */
    @Delete('users/:userId')
    async unassignCase(
        @Param('caseId') caseId: string,
        @Param('userId') userId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseAssignmentService.unassignCase(
            caseId,
            userId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case unassigned successfully');
    }

    /**
     * Get all assignments for a case
     */
    @Get()
    async getCaseAssignments(@Param('caseId') caseId: string) {
        const assignments = await this.caseAssignmentService.getCaseAssignments(caseId);
        return ApiResponseUtil.ok(assignments, 'Case assignments retrieved successfully');
    }

    /**
     * Reassign a case from one user to another
     * Only Super Admin can reassign cases
     */
    @Post('reassign/:oldUserId')
    @IsSuperAdmin()
    async reassignCase(
        @Param('caseId') caseId: string,
        @Param('oldUserId') oldUserId: string,
        @Body() assignCaseDto: AssignCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const assignment = await this.caseAssignmentService.reassignCase(
            caseId,
            oldUserId,
            assignCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(assignment, 'Case reassigned successfully');
    }
}
