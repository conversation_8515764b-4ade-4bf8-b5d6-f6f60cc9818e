import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { CaseEventService } from '../services/case-event.service';
import { CreateCaseEventDto } from '../dto/create-case-event.dto';
import { UpdateCaseEventDto } from '../dto/update-case-event.dto';
import { CaseEventFilterDto } from '../dto/case-event-filter.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { IsLawyer } from '@app/common/roles/decorators';
import { Request } from 'express';
import {
    CaseEventCategory,
    CaseEventType
} from '@app/common/typeorm/entities/tenant/case-event.entity';

@Controller('cases/:caseId/events')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseEventController {
    constructor(private readonly caseEventService: CaseEventService) {}

    /**
     * Create a new event for a case
     */
    @Post()
    @IsLawyer()
    async createEvent(
        @Param('caseId') caseId: string,
        @Body() createCaseEventDto: CreateCaseEventDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const event = await this.caseEventService.createEvent(
            caseId,
            createCaseEventDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(event, 'Case event created successfully');
    }

    /**
     * Update an event
     */
    @Put(':eventId')
    @IsLawyer()
    async updateEvent(
        @Param('caseId') caseId: string,
        @Param('eventId') eventId: string,
        @Body() updateCaseEventDto: UpdateCaseEventDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const event = await this.caseEventService.updateEvent(
            caseId,
            eventId,
            updateCaseEventDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(event, 'Case event updated successfully');
    }

    /**
     * Delete an event
     */
    @Delete(':eventId')
    @IsLawyer()
    async deleteEvent(
        @Param('caseId') caseId: string,
        @Param('eventId') eventId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseEventService.deleteEvent(
            caseId,
            eventId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Case event deleted successfully');
    }

    /**
     * Get all events for a case
     */
    @Get()
    @IsLawyer()
    async getEvents(@Param('caseId') caseId: string, @Query() filterDto: CaseEventFilterDto) {
        // If filter criteria are provided, use filtered query
        if (
            filterDto.category ||
            filterDto.type ||
            filterDto.startDate ||
            filterDto.endDate ||
            filterDto.searchTerm ||
            filterDto.page ||
            filterDto.limit
        ) {
            const paginatedEvents = await this.caseEventService.getFilteredCaseEvents(
                caseId,
                filterDto
            );

            return ApiResponseUtil.ok(paginatedEvents.data, 'Case events retrieved successfully', {
                pagination: paginatedEvents.meta.pagination
            });
        }

        // Otherwise, get all events
        const events = await this.caseEventService.getCaseEvents(caseId);
        return ApiResponseUtil.ok(events, 'Case events retrieved successfully');
    }

    /**
     * Get an event by ID
     */
    @Get(':eventId')
    @IsLawyer()
    async getEventById(@Param('caseId') caseId: string, @Param('eventId') eventId: string) {
        const event = await this.caseEventService.getEventById(caseId, eventId);
        return ApiResponseUtil.ok(event, 'Case event retrieved successfully');
    }

    /**
     * Get events by category
     */
    @Get('category/:category')
    @IsLawyer()
    async getEventsByCategory(
        @Param('caseId') caseId: string,
        @Param('category') category: CaseEventCategory
    ) {
        const events = await this.caseEventService.getEventsByCategory(caseId, category);
        return ApiResponseUtil.ok(
            events,
            `Case events of category ${category} retrieved successfully`
        );
    }

    /**
     * Get events by type
     */
    @Get('type/:type')
    @IsLawyer()
    async getEventsByType(@Param('caseId') caseId: string, @Param('type') type: CaseEventType) {
        const events = await this.caseEventService.getEventsByType(caseId, type);
        return ApiResponseUtil.ok(events, `Case events of type ${type} retrieved successfully`);
    }
}
