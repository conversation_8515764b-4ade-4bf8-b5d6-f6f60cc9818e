import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { CaseContactService } from '../services/case-contact.service';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { <PERSON><PERSON>awyer } from '@app/common/roles/decorators';
import { Request } from 'express';
import { ContactType } from '@app/common/typeorm/entities/tenant/case-contact.entity';

@Controller('cases/:caseId/contacts')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class CaseContactController {
    constructor(private readonly caseContactService: CaseContactService) {}

    /**
     * Create a new contact for a case
     */
    @Post()
    @IsLawyer()
    async createContact(
        @Param('caseId') caseId: string,
        @Body() createContactDto: CreateContactDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const contact = await this.caseContactService.createContact(
            caseId,
            createContactDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(contact, 'Contact created successfully');
    }

    /**
     * Update a contact
     */
    @Put(':contactId')
    @IsLawyer()
    async updateContact(
        @Param('caseId') caseId: string,
        @Param('contactId') contactId: string,
        @Body() updateContactDto: UpdateContactDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const contact = await this.caseContactService.updateContact(
            caseId,
            contactId,
            updateContactDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(contact, 'Contact updated successfully');
    }

    /**
     * Delete a contact
     */
    @Delete(':contactId')
    @IsLawyer()
    async deleteContact(
        @Param('caseId') caseId: string,
        @Param('contactId') contactId: string,
        @Req() request: Request
    ) {
        const user = request['user'];
        await this.caseContactService.deleteContact(
            caseId,
            contactId,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(null, 'Contact deleted successfully');
    }

    /**
     * Get all contacts for a case
     */
    @Get()
    @IsLawyer()
    async getContacts(@Param('caseId') caseId: string, @Query('type') type?: ContactType) {
        let contacts;

        if (type) {
            contacts = await this.caseContactService.getContactsByType(caseId, type);
            return ApiResponseUtil.ok(contacts, `Contacts of type ${type} retrieved successfully`);
        } else {
            contacts = await this.caseContactService.getCaseContacts(caseId);
            return ApiResponseUtil.ok(contacts, 'Contacts retrieved successfully');
        }
    }

    /**
     * Get a contact by ID
     */
    @Get(':contactId')
    @IsLawyer()
    async getContactById(@Param('caseId') caseId: string, @Param('contactId') contactId: string) {
        const contact = await this.caseContactService.getContactById(caseId, contactId);
        return ApiResponseUtil.ok(contact, 'Contact retrieved successfully');
    }
}
