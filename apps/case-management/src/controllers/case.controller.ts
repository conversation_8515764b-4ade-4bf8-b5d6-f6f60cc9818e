import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { CaseService } from '../services/case.service';
import { CreateCaseDto } from '../dto/create-case.dto';
import { UpdateCaseDto } from '../dto/update-case.dto';
import { CaseFilterDto } from '../dto/case-filter.dto';
import { ApiResponseUtil } from '@app/common/api-response';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { TenantGuard } from '@app/common/multi-tenancy';
import { PermissionGuard } from '@app/common/guards/permission.guard';
import { Permission } from '@app/common/permissions/enums/permission.enum';
import { Request } from 'express';

import { CaseType, CasePriority } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { HasPermission } from '@app/common/permissions/permission.decorators';
import { ResourceType } from '@app/common/permissions/permission.constants';
import {
    RequireSuperAdmin,
    SuperAdminOrRoleGroup,
} from '@app/common/permissions/role-group.decorators';

@Controller('cases')
@UseGuards(JwtGuard, TenantGuard, RolesGuard,PermissionGuard)
export class CaseController {
    constructor(private readonly caseService: CaseService) {}

    /**
     * Create a new case
     * Requires: Permission to create cases AND role group access
     */
    @Post()
    @HasPermission(ResourceType.CASE, Permission.CREATE)
    async createCase(@Body() createCaseDto: CreateCaseDto, @Req() request: Request) {
        const user = request['user'];
        const newCase = await this.caseService.createCase(
            createCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.created(newCase, 'Case created successfully');
    }

    /**
     * Get a case by ID
     */
    @Get(':id')
    async getCaseById(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const caseEntity = await this.caseService.findCaseById(id, user.systemUserId);
        return ApiResponseUtil.ok(caseEntity, 'Case retrieved successfully');
    }

    /**
     * Get comprehensive case details including all related information
     */
    @Get(':id/details')
    async getCaseDetails(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        const caseDetails = await this.caseService.getCaseDetails(id, user.systemUserId);
        await this.caseService.auditCaseAccess(
            id,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );
        return ApiResponseUtil.ok(caseDetails, 'Case details retrieved successfully');
    }

    /**
     * Get a case by case number
     */
    @Get('number/:caseNumber')
    async getCaseByCaseNumber(@Param('caseNumber') caseNumber: string, @Req() request: Request) {
        const user = request['user'];
        const caseEntity = await this.caseService.findCaseByCaseNumber(
            caseNumber,
            user.systemUserId
        );
        return ApiResponseUtil.ok(caseEntity, 'Case retrieved successfully');
    }

    /**
     * Get all cases (requires READ permission and role group access)
     */
    @Get()
    @HasPermission(ResourceType.CASE, Permission.READ)
    async getCases(@Query() filterDto: CaseFilterDto) {
        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(paginatedCases.data, 'Cases retrieved successfully', {
            pagination: paginatedCases.meta.pagination
        });
    }

    /**
     * Filter cases by type
     * Allows filtering cases by their type (e.g., litigation, consultation)
     */
    @Get('filter/type/:type')
    async filterByType(
        @Param('type') type: CaseType,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10
    ) {
        const filterDto = new CaseFilterDto();
        filterDto.type = type;
        filterDto.page = page;
        filterDto.limit = limit;

        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(
            paginatedCases.data,
            `Cases of type ${type} retrieved successfully`,
            { pagination: paginatedCases.meta.pagination }
        );
    }

    /**
     * Filter cases by status
     * Allows filtering cases by their status
     */
    @Get('filter/status/:status')
    async filterByStatus(
        @Param('status') status: CaseStatus,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10
    ) {
        const filterDto = new CaseFilterDto();
        filterDto.status = status;
        filterDto.page = page;
        filterDto.limit = limit;

        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(
            paginatedCases.data,
            `Cases with status ${status} retrieved successfully`,
            { pagination: paginatedCases.meta.pagination }
        );
    }

    /**
     * Filter cases by date range
     * Allows filtering cases by their creation date range
     */
    @Get('filter/date-range')
    async filterByDateRange(
        @Query('startDate') startDate: string,
        @Query('endDate') endDate: string,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10
    ) {
        const filterDto = new CaseFilterDto();
        filterDto.createdAfter = startDate;
        filterDto.createdBefore = endDate;
        filterDto.page = page;
        filterDto.limit = limit;

        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(
            paginatedCases.data,
            'Cases within date range retrieved successfully',
            { pagination: paginatedCases.meta.pagination }
        );
    }

    /**
     * Filter cases by priority
     * Allows filtering cases by their priority level
     */
    @Get('filter/priority/:priority')
    async filterByPriority(
        @Param('priority') priority: CasePriority,
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10
    ) {
        const filterDto = new CaseFilterDto();
        filterDto.priority = priority;
        filterDto.page = page;
        filterDto.limit = limit;

        const paginatedCases = await this.caseService.findCases(filterDto);
        return ApiResponseUtil.ok(
            paginatedCases.data,
            `Cases with priority ${priority} retrieved successfully`,
            { pagination: paginatedCases.meta.pagination }
        );
    }

    /**
     * Quick search for cases - optimized for fast response (<1ms)
     * Supports searching by name, case ID, type, or handler
     */
    @Get('search/quick')
    async quickSearch(
        @Query('term') searchTerm: string,
        @Query('limit') limit: number = 5,
        @Req() request: Request
    ) {
        if (!searchTerm || searchTerm.length < 2) {
            return ApiResponseUtil.ok([], 'Please provide at least 2 characters to search');
        }

        const user = request['user'];
        const cases = await this.caseService.quickSearch(searchTerm, limit);

        // Log search for audit purposes
        await this.caseService.auditCaseAccess(
            'N/A', // No specific case ID for search
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(cases, 'Search results retrieved successfully');
    }

    /**
     * Global search across cases, clients, and documents
     * Provides a unified search experience across the entire system
     */
    @Get('search/global')
    async globalSearch(
        @Query('term') searchTerm: string,
        @Query('limit') limit: number = 10,
        @Req() request: Request
    ) {
        if (!searchTerm || searchTerm.length < 2) {
            return ApiResponseUtil.ok(
                {
                    cases: [],
                    clients: [],
                    documents: []
                },
                'Please provide at least 2 characters to search'
            );
        }

        const user = request['user'];
        const results = await this.caseService.globalSearch(searchTerm, limit);

        // Log search for audit purposes
        await this.caseService.auditCaseAccess(
            'N/A', // No specific case ID for search
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );

        return ApiResponseUtil.ok(results, 'Global search results retrieved successfully');
    }

    /**
     * Search cases by ID
     * Allows looking up a specific case by its unique ID
     */
    @Get('search/id/:caseNumber')
    async searchById(@Param('caseNumber') caseNumber: string, @Req() request: Request) {
        const user = request['user'];
        const caseEntity = await this.caseService.findCaseByCaseNumber(
            caseNumber,
            user.systemUserId
        );
        return ApiResponseUtil.ok(caseEntity, 'Case retrieved successfully');
    }

    /**
     * Update a case (requires UPDATE permission and role group access)
     */
    @Patch(':id')
    @HasPermission(ResourceType.CASE, Permission.UPDATE)
    @SuperAdminOrRoleGroup('conveyancers')
    async updateCase(
        @Param('id') id: string,
        @Body() updateCaseDto: UpdateCaseDto,
        @Req() request: Request
    ) {
        const user = request['user'];
        const updatedCase = await this.caseService.updateCase(
            id,
            updateCaseDto,
            user.systemUserId,
            user.preferred_username || user.email,
            request
        );
        return ApiResponseUtil.ok(updatedCase, 'Case updated successfully');
    }

    /**
     * Change case status
     */
    // @Patch(':id/status/:status')
    // @IsLawyer()
    // async changeStatus(
    //     @Param('id') id: string,
    //     @Param('status') status: CaseStatus,
    //     @Req() request: Request
    // ) {
    //     const user = request['user'];
    //     const updatedCase = await this.caseService.changeStatus(
    //         id,
    //         status,
    //         user.systemUserId,
    //         user.preferred_username || user.email,
    //         request
    //     );

    //     return ApiResponseUtil.ok(updatedCase, `Case status changed to ${status}`);
    // }

    /**
     * Close a case with validation
     */
    // @Patch(':id/close')
    // @IsManager()
    // async closeCase(
    //     @Param('id') id: string,
    //     @Body('closureReason') closureReason: string,
    //     @Req() request: Request
    // ) {
    //     if (!closureReason) {
    //         throw new BadRequestException('Closure reason is required');
    //     }

    //     const user = request['user'];

    //     // Check if case can be closed
    //     const canCloseCheck = await this.caseService.canCloseCaseCheck(id);
    //     if (!canCloseCheck.canClose) {
    //         return ApiResponseUtil.badRequest(`Cannot close case: ${canCloseCheck.reason}`);
    //     }

    //     // Close the case
    //     const updatedCase = await this.caseService.changeStatus(
    //         id,
    //         CaseStatus.CLOSED,
    //         user.systemUserId,
    //         user.preferred_username || user.email,
    //         request,
    //         { closureReason }
    //     );

    //     return ApiResponseUtil.ok(updatedCase, 'Case closed successfully');
    // }

    /**
     * Delete a case (Super Admin only)
     */
    @Delete(':id')
    @RequireSuperAdmin()
    async deleteCase(@Param('id') id: string, @Req() request: Request) {
        const user = request['user'];
        await this.caseService.deleteCase(id, user.systemUserId);
        return ApiResponseUtil.ok(null, 'Case deleted successfully');
    }


}
