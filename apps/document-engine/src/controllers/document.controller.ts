import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UploadedFile,
    UseGuards,
    UseInterceptors
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { DocumentService } from '../document/services/document.service';
import { Document } from '@app/common/typeorm/entities';
import { TenantGuard } from '@app/common/multi-tenancy';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { Request } from 'express';
import { IsLawyer } from '@app/common/roles/decorators';
import { ApiResponseUtil } from '@app/common/api-response';

// Define Multer file interface
interface MulterFile {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
    filename: string;
    path: string;
    buffer: Buffer;
}

@Controller('documents')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class DocumentController {
    constructor(private readonly documentService: DocumentService) {}

    @Post()
    @IsLawyer()
    @UseInterceptors(FileInterceptor('file'))
    async createDocument(
        @UploadedFile() file: MulterFile,
        @Body()
        data: {
            name: string;
            description?: string;
            caseId: string;
            folderId?: string;
        },
        @Req() request: Request
    ) {
        const user = request['user'];

        const document = await this.documentService.createDocument({
            name: data.name,
            description: data.description,
            caseId: data.caseId,
            folderId: data.folderId,
            fileBuffer: file.buffer,
            filename: file.originalname,
            mimeType: file.mimetype,
            createdBy: user.systemUserId
        });

        return ApiResponseUtil.created(document, 'Document created successfully');
    }

    @Get()
    @IsLawyer()
    async findAll(@Query('caseId') caseId?: string, @Query('folderId') folderId?: string) {
        let documents: Document[];

        if (caseId && !folderId) {
            documents = await this.documentService.documentRepo.findRootDocumentsByCaseId(caseId);
        } else if (folderId) {
            documents = await this.documentService.getDocumentsByFolderId(folderId);
        } else if (caseId) {
            documents = await this.documentService.getDocumentsByCaseId(caseId);
        } else {
            documents = [];
        }

        return ApiResponseUtil.ok(documents, 'Documents retrieved successfully');
    }

    @Get('search')
    @IsLawyer()
    async search(
        @Query('q') searchTerm: string,
        @Query('caseId') caseId?: string,
        @Query('folderId') folderId?: string,
        @Query('limit') limit?: string
    ) {
        const documents = await this.documentService.searchDocuments(searchTerm, {
            folderId,
            caseId,
            limit: limit ? parseInt(limit, 10) : undefined
        });

        return ApiResponseUtil.ok(documents, 'Documents searched successfully');
    }

    @Get(':id')
    @IsLawyer()
    async findOne(@Param('id') id: string) {
        const document = await this.documentService.getDocumentById(id);
        if (!document) {
            return ApiResponseUtil.notFound('Document not found');
        }
        return ApiResponseUtil.ok(document, 'Document retrieved successfully');
    }

    @Get(':id/download')
    @IsLawyer()
    async downloadDocument(@Param('id') id: string) {
        const downloadInfo = await this.documentService.getDocumentDownloadUrl(id);
        return ApiResponseUtil.ok(downloadInfo, 'Download URL generated successfully');
    }

    @Put(':id')
    @IsLawyer()
    async updateDocument(
        @Param('id') id: string,
        @Body()
        data: {
            name?: string;
            description?: string;
            folderId?: string;
            caseId?: string;
        },
        @Req() request: Request
    ) {
        const user = request['user'];

        const document = await this.documentService.updateDocument(id, {
            ...data,
            updatedBy: user.systemUserId
        });

        if (!document) {
            return ApiResponseUtil.notFound('Document not found');
        }

        return ApiResponseUtil.ok(document, 'Document updated successfully');
    }

    @Put(':id/file')
    @IsLawyer()
    @UseInterceptors(FileInterceptor('file'))
    async replaceDocumentFile(
        @Param('id') id: string,
        @UploadedFile() file: MulterFile,
        @Req() request: Request
    ) {
        const user = request['user'];

        const document = await this.documentService.replaceDocumentFile(id, {
            fileBuffer: file.buffer,
            filename: file.originalname,
            mimeType: file.mimetype,
            updatedBy: user.systemUserId
        });

        return ApiResponseUtil.ok(document, 'Document file replaced successfully');
    }

    @Delete(':id')
    @IsLawyer()
    async removeDocument(@Param('id') id: string) {
        const result = await this.documentService.deleteDocument(id);
        return ApiResponseUtil.ok({ success: result }, 'Document deleted successfully');
    }
}
