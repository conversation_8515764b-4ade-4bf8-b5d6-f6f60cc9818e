import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    Req,
    UseGuards
} from '@nestjs/common';
import { DocumentFolderService } from '../document/services/document-folder.service';
import { DocumentFolder } from '@app/common/typeorm/entities';
import { TenantGuard } from '@app/common/multi-tenancy';
import { JwtGuard } from '@app/common/guards/jwt.guard';
import { RolesGuard } from '@app/common/guards/roles.guard';
import { Request } from 'express';
import { IsLawyer } from '@app/common/roles/decorators';
import { ApiResponseUtil } from '@app/common/api-response';

@Controller('document-folders')
@UseGuards(JwtGuard, TenantGuard, RolesGuard)
export class DocumentFolderController {
    constructor(private readonly folderService: DocumentFolderService) {}

    @Post()
    @IsLawyer()
    async createFolder(
        @Body()
        data: {
            name: string;
            description?: string;
            caseId: string;
            parentFolderId?: string;
        },
        @Req() request: Request
    ) {
        const user = request['user'];

        const folder = await this.folderService.createFolder({
            name: data.name,
            description: data.description,
            caseId: data.caseId,
            parentFolderId: data.parentFolderId,
            createdBy: user.systemUserId
        });

        return ApiResponseUtil.created(folder, 'Folder created successfully');
    }

    @Get()
    @IsLawyer()
    async findFolders(
        @Query('caseId') caseId: string,
        @Query('parentFolderId') parentFolderId?: string
    ) {
        if (!caseId) {
            return ApiResponseUtil.badRequest('Case ID is required');
        }

        let folders: DocumentFolder[];

        if (parentFolderId) {
            // Get child folders of a specific parent
            folders = await this.folderService.folderRepo.findChildFolders(parentFolderId);
        } else {
            // Get root folders for the case
            folders = await this.folderService.folderRepo.findRootFoldersByCaseId(caseId);
        }

        return ApiResponseUtil.ok(folders, 'Folders retrieved successfully');
    }

    @Get('search')
    @IsLawyer()
    async searchFolders(
        @Query('caseId') caseId: string,
        @Query('q') searchTerm: string,
        @Query('limit') limit?: string
    ) {
        if (!caseId) {
            return ApiResponseUtil.badRequest('Case ID is required');
        }

        if (!searchTerm) {
            return ApiResponseUtil.badRequest('Search term is required');
        }

        const folders = await this.folderService.folderRepo.searchInCase(
            caseId,
            searchTerm,
            limit ? parseInt(limit, 10) : undefined
        );

        return ApiResponseUtil.ok(folders, 'Folders searched successfully');
    }

    @Get(':id')
    @IsLawyer()
    async findOne(@Param('id') id: string, @Query('caseId') caseId?: string) {
        let folder: DocumentFolder | null;

        if (caseId) {
            // Security check: ensure folder belongs to the specified case
            folder = await this.folderService.folderRepo.findByCaseIdAndId(caseId, id);
        } else {
            folder = await this.folderService.getFolderById(id);
        }

        if (!folder) {
            return ApiResponseUtil.notFound('Folder not found');
        }

        return ApiResponseUtil.ok(folder, 'Folder retrieved successfully');
    }

    @Get(':id/path')
    @IsLawyer()
    async getFolderPath(@Param('id') id: string) {
        const path = await this.folderService.folderRepo.getFolderPath(id);
        return ApiResponseUtil.ok(path, 'Folder path retrieved successfully');
    }

    @Put(':id')
    @IsLawyer()
    async updateFolder(
        @Param('id') id: string,
        @Body()
        data: {
            name?: string;
            description?: string;
            parentFolderId?: string;
        },
        @Req() request: Request
    ) {
        const user = request['user'];

        const folder = await this.folderService.updateFolder(id, {
            ...data,
            updatedBy: user.systemUserId
        });

        if (!folder) {
            return ApiResponseUtil.notFound('Folder not found');
        }

        return ApiResponseUtil.ok(folder, 'Folder updated successfully');
    }

    @Delete(':id')
    @IsLawyer()
    async removeFolder(@Param('id') id: string) {
        const result = await this.folderService.deleteFolder(id);
        return ApiResponseUtil.ok({ success: result }, 'Folder deleted successfully');
    }
}
