import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DocumentRepository } from '../../repositories/document.repository';
import { S3StorageService } from './s3-storage.service';
import { Document } from '@app/common/typeorm/entities';

/**
 * Service for document management - simplified case-centric approach
 */
@Injectable()
export class DocumentService {
    private readonly logger = new Logger(DocumentService.name);

    constructor(
        public readonly documentRepo: DocumentRepository,
        private readonly s3StorageService: S3StorageService
    ) {}

    /**
     * Create a new document
     */
    async createDocument(data: {
        name: string;
        description?: string;
        caseId: string;
        folderId?: string;
        fileBuffer: Buffer;
        filename: string;
        mimeType: string;
        fileExtension?: string;
        createdBy: string;
    }): Promise<Document> {
        // Upload file to S3 first
        const uploadResult = await this.s3StorageService.uploadFile(
            data.caseId,
            data.folderId || null,
            data.filename,
            data.fileBuffer,
            data.mimeType
        );

        // Create document metadata
        const document = await this.documentRepo.create({
            name: data.name,
            description: data.description,
            caseId: data.caseId,
            folderId: data.folderId,
            s3Key: uploadResult.s3Key,
            s3Bucket: uploadResult.s3Bucket,
            fileName: data.filename,
            fileExtension: data.fileExtension || this.getFileExtension(data.filename),
            mimeType: data.mimeType,
            sizeInBytes: Buffer.byteLength(data.fileBuffer).toString(),
            checksum: uploadResult.checksum,
            createdBy: data.createdBy,
            createdAt: new Date(),
            updatedAt: new Date(),
            lastModifiedBy: data.createdBy
        });

        // Save document
        return this.documentRepo.save(document);
    }

    /**
     * Get a document by ID
     */
    async getDocumentById(id: string): Promise<Document | null> {
        return this.documentRepo.findOneBy({ id });
    }

    /**
     * Get documents by case ID
     */
    async getDocumentsByCaseId(caseId: string): Promise<Document[]> {
        return this.documentRepo.findByCaseId(caseId);
    }

    /**
     * Get documents by folder ID
     */
    async getDocumentsByFolderId(folderId: string): Promise<Document[]> {
        return this.documentRepo.findByFolderId(folderId);
    }

    /**
     * Generate a presigned URL for downloading a document
     */
    async getDocumentDownloadUrl(documentId: string): Promise<{ url: string; filename: string }> {
        const document = await this.getDocumentById(documentId);
        if (!document) {
            throw new NotFoundException(`Document with ID ${documentId} not found`);
        }

        // Generate presigned URL
        const url = await this.s3StorageService.getDownloadUrl(document.s3Key, document.fileName);

        return {
            url,
            filename: document.fileName
        };
    }

    /**
     * Update document metadata
     */
    async updateDocument(
        id: string,
        data: {
            name?: string;
            description?: string;
            folderId?: string;
            caseId?: string;
            updatedBy: string;
        }
    ): Promise<Document | null> {
        const document = await this.getDocumentById(id);
        if (!document) {
            return null;
        }

        // Update fields if provided
        if (data.name !== undefined) {
            document.name = data.name;
        }

        if (data.description !== undefined) {
            document.description = data.description;
        }

        if (data.folderId !== undefined) {
            document.folderId = data.folderId;
        }

        if (data.caseId !== undefined) {
            document.caseId = data.caseId;
        }

        // Update timestamp and modifier
        document.updatedAt = new Date();
        document.lastModifiedBy = data.updatedBy;

        return this.documentRepo.save(document);
    }

    /**
     * Delete a document
     */
    async deleteDocument(id: string): Promise<boolean> {
        const document = await this.getDocumentById(id);
        if (!document) {
            return false;
        }

        try {
            // Delete from S3 first
            await this.s3StorageService.deleteFile(document.s3Key);

            // Delete from database
            await this.documentRepo.removeById(id);

            return true;
        } catch (error) {
            this.logger.error(`Error deleting document ${id}: ${error.message}`, error.stack);
            throw new Error(`Failed to delete document: ${error.message}`);
        }
    }

    /**
     * Search for documents
     */
    async searchDocuments(
        searchTerm: string,
        options?: {
            folderId?: string;
            caseId?: string;
            limit?: number;
        }
    ): Promise<Document[]> {
        return this.documentRepo.search(
            searchTerm,
            options?.folderId,
            options?.caseId,
            options?.limit || 10
        );
    }

    /**
     * Replace document file (upload new file and update metadata)
     */
    async replaceDocumentFile(
        id: string,
        data: {
            fileBuffer: Buffer;
            filename: string;
            mimeType?: string;
            updatedBy: string;
        }
    ): Promise<Document> {
        const document = await this.getDocumentById(id);
        if (!document) {
            throw new NotFoundException(`Document with ID ${id} not found`);
        }

        // Delete old file from S3
        await this.s3StorageService.deleteFile(document.s3Key);

        // Upload new file to S3
        const uploadResult = await this.s3StorageService.uploadFile(
            document.caseId,
            document.folderId || null,
            data.filename,
            data.fileBuffer,
            data.mimeType || document.mimeType
        );

        // Update document metadata
        document.s3Key = uploadResult.s3Key;
        document.s3Bucket = uploadResult.s3Bucket;
        document.fileName = data.filename;
        document.fileExtension = this.getFileExtension(data.filename);
        document.mimeType = data.mimeType || document.mimeType;
        document.sizeInBytes = Buffer.byteLength(data.fileBuffer).toString();
        document.checksum = uploadResult.checksum;
        document.updatedAt = new Date();
        document.lastModifiedBy = data.updatedBy;

        return this.documentRepo.save(document);
    }

    /**
     * Get file extension from filename
     */
    private getFileExtension(filename: string): string {
        const parts = filename.split('.');
        return parts.length > 1 ? parts.pop()?.toLowerCase() || '' : '';
    }
}
