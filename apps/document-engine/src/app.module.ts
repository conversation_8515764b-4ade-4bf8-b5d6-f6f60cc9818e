import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonModule } from '@app/common';
import { HealthModule } from './health/health.module';
import { DocumentController } from './controllers/document.controller';
import { DocumentModule } from './document/document.module';
import { DocumentFolderController } from './controllers/document-folder.controller';

@Module({
    imports: [
        CommonModule,
        ConfigModule.forRoot({
            isGlobal: true
        }),
        HealthModule,
        DocumentModule
    ],
    controllers: [DocumentController, DocumentFolderController],
    providers: []
})
export class AppModule {}
