{"info": {"_postman_id": "auth-controller-tests-001", "name": "Auth Controller Tests", "description": "Collection for testing Auth Controller endpoints including user promotion/demotion functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"{{admin_username}}\",\n  \"password\": \"{{admin_password}}\",\n  \"realm\": \"{{realm}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.environment.set('access_token', response.data.accessToken);", "        console.log('Access token saved to environment');", "    }", "}"], "type": "text/javascript"}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\",\n  \"realm\": \"{{realm}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}]}, {"name": "User Management", "item": [{"name": "Create Conveyancer User (Normal)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"conveyancer_user\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"ConveyancerUser123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Conveyancer\",\n  \"roleGroupKey\": \"conveyancers\",\n  \"isRoleGroupAdmin\": false,\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users", "host": ["{{base_url}}"], "path": ["auth", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should create conveyancer user successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.data.keycloakUserId).to.exist;", "    pm.expect(response.data.roleGroupKey).to.eql('conveyancers');", "    pm.expect(response.data.isRoleGroupAdmin).to.be.false;", "    pm.environment.set('conveyancer_user_id', response.data.keycloakUserId);", "});"], "type": "text/javascript"}}]}, {"name": "Create Conveyancer Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"conveyancer_admin\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"ConveyancerAdmin123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"AdminConveyancer\",\n  \"roleGroupKey\": \"conveyancers\",\n  \"isRoleGroupAdmin\": true,\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users", "host": ["{{base_url}}"], "path": ["auth", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should create conveyancer admin successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.data.keycloakUserId).to.exist;", "    pm.expect(response.data.roleGroupKey).to.eql('conveyancers');", "    pm.expect(response.data.isRoleGroupAdmin).to.be.true;", "    pm.environment.set('conveyancer_admin_id', response.data.keycloakUserId);", "});"], "type": "text/javascript"}}]}, {"name": "Create Finance User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"finance_user\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"FinanceUser123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Finance\",\n  \"roleGroupKey\": \"finance\",\n  \"isRoleGroupAdmin\": false,\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users", "host": ["{{base_url}}"], "path": ["auth", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should create finance user successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.data.keycloakUserId).to.exist;", "    pm.expect(response.data.roleGroupKey).to.eql('finance');", "    pm.expect(response.data.isRoleGroupAdmin).to.be.false;", "    pm.environment.set('finance_user_id', response.data.keycloakUserId);", "});"], "type": "text/javascript"}}]}, {"name": "Promote Conveyancer User to Admin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{conveyancer_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{conveyancer_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should promote conveyancer user to admin successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('promoted to admin');", "    pm.expect(response.message).to.include('conveyancers');", "});"], "type": "text/javascript"}}]}, {"name": "Demote Conveyancer Admin to User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": false,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{conveyancer_admin_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{conveyancer_admin_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should demote conveyancer admin to user successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('demoted to user');", "    pm.expect(response.message).to.include('conveyancers');", "});"], "type": "text/javascript"}}]}, {"name": "Promote Finance User to Admin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"finance\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{finance_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{finance_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should promote finance user to admin successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('promoted to admin');", "    pm.expect(response.message).to.include('finance');", "});"], "type": "text/javascript"}}]}, {"name": "Test User Login After Promotion", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"conveyancer_user\",\n  \"password\": \"ConveyancerUser123!\",\n  \"realm\": \"{{realm}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('User should login successfully after promotion', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.data.accessToken).to.exist;", "    pm.expect(response.data.user.roleGroups).to.exist;", "    // Check if user has admin role in conveyancers group", "    const conveyancerRole = response.data.user.roleGroups.find(rg => rg.key === 'conveyancers');", "    pm.expect(conveyancerRole).to.exist;", "    pm.expect(conveyancerRole.isAdmin).to.be.true;", "    pm.environment.set('promoted_user_token', response.data.accessToken);", "});"], "type": "text/javascript"}}]}]}, {"name": "Role Group Management", "item": [{"name": "Create Conveyancers Role Group", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"key\": \"conveyancers\",\n  \"label\": \"Conveyancers\",\n  \"description\": \"Role group for conveyancing professionals\",\n  \"resourcePermissions\": [\n    {\n      \"resource\": \"CASE\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\", \"ASSIGN\"]\n    },\n    {\n      \"resource\": \"DOCUMENT\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\"]\n    },\n    {\n      \"resource\": \"CLIENT\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\"]\n    },\n    {\n      \"resource\": \"TASK\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\"]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/auth/role-groups", "host": ["{{base_url}}"], "path": ["auth", "role-groups"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should create conveyancers role group successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.data.key).to.eql('conveyancers');", "    pm.expect(response.data.rolesCreated).to.include('conveyancers_user');", "    pm.expect(response.data.rolesCreated).to.include('conveyancers_admin');", "});"], "type": "text/javascript"}}]}, {"name": "Create Finance Role Group", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"key\": \"finance\",\n  \"label\": \"Finance Team\",\n  \"description\": \"Role group for finance professionals\",\n  \"resourcePermissions\": [\n    {\n      \"resource\": \"DOCUMENT\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\", \"EXPORT\"]\n    },\n    {\n      \"resource\": \"REPORT\",\n      \"permissions\": [\"READ\", \"CREATE\", \"EXPORT\"]\n    },\n    {\n      \"resource\": \"CLIENT\",\n      \"permissions\": [\"READ\"]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/auth/role-groups", "host": ["{{base_url}}"], "path": ["auth", "role-groups"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should create finance role group successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.data.key).to.eql('finance');", "    pm.expect(response.data.rolesCreated).to.include('finance_user');", "    pm.expect(response.data.rolesCreated).to.include('finance_admin');", "});"], "type": "text/javascript"}}]}, {"name": "Get Role Groups", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}], "url": {"raw": "{{base_url}}/auth/role-groups", "host": ["{{base_url}}"], "path": ["auth", "role-groups"]}}}, {"name": "Update Role Group Permissions", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"resource\": \"CASE\",\n  \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\", \"DELETE\"]\n}"}, "url": {"raw": "{{base_url}}/auth/role-groups/conveyancers/permissions", "host": ["{{base_url}}"], "path": ["auth", "role-groups", "conveyancers", "permissions"]}}}, {"name": "Get Permission Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/role-groups/permission-options", "host": ["{{base_url}}"], "path": ["auth", "role-groups", "permission-options"]}}}]}, {"name": "Error Scenarios & Edge Cases", "item": [{"name": "Promote Non-existent User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/123e4567-e89b-12d3-a456-426614174000/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "123e4567-e89b-12d3-a456-426614174000", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should return 400 for non-existent user', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('not found');", "});"], "type": "text/javascript"}}]}, {"name": "Try to Promote User Already Admin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{conveyancer_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{conveyancer_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should return 400 when trying to promote already admin user', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('already an admin');", "});"], "type": "text/javascript"}}]}, {"name": "Try to Demote User Not Admin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"finance\",\n  \"promoteToAdmin\": false,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{finance_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{finance_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should return 400 when trying to demote non-admin user', function () {", "    pm.response.to.have.status(400);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('not an admin');", "});"], "type": "text/javascript"}}]}, {"name": "Promote User with Invalid Role Group", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"invalid-role-group\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{test_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{test_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should return 400 for invalid role group', function () {", "    pm.response.to.have.status(400);", "});"], "type": "text/javascript"}}]}, {"name": "Promote Without Super Admin Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{regular_user_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{test_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{test_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Should return 403 for non-super admin', function () {", "    pm.response.to.have.status(403);", "});"], "type": "text/javascript"}}]}]}, {"name": "Test Workflow - Complete Role Group Testing", "item": [{"name": "Step 1: <PERSON><PERSON> as Super Admin", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"{{admin_username}}\",\n  \"password\": \"{{admin_password}}\",\n  \"realm\": \"{{realm}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Super Admin login successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.data.accessToken);", "    pm.expect(response.data.user.isSuperAdmin).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Step 2: Create Role Groups", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"key\": \"conveyancers\",\n  \"label\": \"Conveyancers\",\n  \"description\": \"Role group for conveyancing professionals\",\n  \"resourcePermissions\": [\n    {\n      \"resource\": \"CASE\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\", \"ASSIGN\"]\n    },\n    {\n      \"resource\": \"DOCUMENT\",\n      \"permissions\": [\"READ\", \"CREATE\", \"UPDATE\"]\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/auth/role-groups", "host": ["{{base_url}}"], "path": ["auth", "role-groups"]}}}, {"name": "Step 3: Create Test User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"workflow_test_user\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"WorkflowTest123!\",\n  \"firstName\": \"Workflow\",\n  \"lastName\": \"TestUser\",\n  \"roleGroupKey\": \"conveyancers\",\n  \"isRoleGroupAdmin\": false,\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users", "host": ["{{base_url}}"], "path": ["auth", "users"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Test user created successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.environment.set('workflow_user_id', response.data.keycloakUserId);", "    pm.expect(response.data.isRoleGroupAdmin).to.be.false;", "});"], "type": "text/javascript"}}]}, {"name": "Step 4: Promote User to Admin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "x-realm", "value": "{{realm}}"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"roleGroupKey\": \"conveyancers\",\n  \"promoteToAdmin\": true,\n  \"realm\": \"{{realm}}\",\n  \"adminPassword\": \"{{admin_password}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/users/{{workflow_user_id}}/promote", "host": ["{{base_url}}"], "path": ["auth", "users", "{{workflow_user_id}}", "promote"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('User promoted to admin successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.message).to.include('promoted to admin');", "});"], "type": "text/javascript"}}]}, {"name": "Step 5: V<PERSON>fy <PERSON> with <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-realm", "value": "{{realm}}"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"workflow_test_user\",\n  \"password\": \"WorkflowTest123!\",\n  \"realm\": \"{{realm}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('User login with admin role successful', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    const conveyancerRole = response.data.user.roleGroups.find(rg => rg.key === 'conveyancers');", "    pm.expect(conveyancerRole).to.exist;", "    pm.expect(conveyancerRole.isAdmin).to.be.true;", "});"], "type": "text/javascript"}}]}]}], "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string", "description": "Base URL for the API"}, {"key": "realm", "value": "your-tenant-realm", "type": "string", "description": "Tenant realm name"}, {"key": "tenant_id", "value": "your-tenant-id", "type": "string", "description": "Tenant <PERSON>"}, {"key": "admin_username", "value": "admin", "type": "string", "description": "Super admin username"}, {"key": "admin_password", "value": "admin-password", "type": "string", "description": "Super admin password"}, {"key": "access_token", "value": "", "type": "string", "description": "JWT access token (auto-populated)"}, {"key": "conveyancer_user_id", "value": "", "type": "string", "description": "Conveyancer user ID (auto-populated)"}, {"key": "conveyancer_admin_id", "value": "", "type": "string", "description": "Conveyancer admin ID (auto-populated)"}, {"key": "finance_user_id", "value": "", "type": "string", "description": "Finance user ID (auto-populated)"}, {"key": "workflow_user_id", "value": "", "type": "string", "description": "Workflow test user ID (auto-populated)"}, {"key": "promoted_user_token", "value": "", "type": "string", "description": "Token for promoted user (auto-populated)"}]}