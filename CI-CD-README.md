# CI/CD Workflow for Microservices

This document describes the CI/CD workflow for the microservices architecture in this project.

## Overview

The CI/CD pipeline is designed to:

1. Automatically detect which services have changed
2. Build, test, and containerize each changed service
3. Push container images to AWS ECR
4. Deploy services to the appropriate environment

## GitHub Actions Workflows

### Main CI/CD Pipeline

The main CI/CD pipeline is defined in `.github/workflows/ci-cd.yml`. This workflow:

- Triggers on pushes to `main` and `develop` branches
- Detects which services have changed
- Builds, tests, and containerizes each changed service
- Pushes container images to AWS ECR

### Service Deployment

The service deployment workflow is defined in `.github/workflows/service-deployment.yml`. This workflow:

- Is called by the main CI/CD pipeline
- Deploys a specific service to a specific environment
- Updates Kubernetes deployments

## Local Development

### Prerequisites

- Docker and Docker Compose
- AWS CLI (for pushing images to ECR)
- Node.js and Yarn


### Building and Pushing Docker Images

Docker images are automatically built and pushed to ECR by the GitHub Actions CI/CD workflow when changes are pushed to the `main` or `develop` branches. There's no need for manual image building and pushing as part of the normal development workflow.

If you need to manually build a Docker image locally for testing, you can use standard Docker commands:

```bash
# Build a specific service
docker build -t <service-name>:local -f ./apps/<service-name>/Dockerfile .
```

## Dockerfiles

Each microservice has its own Dockerfile in its respective directory:

- `apps/core/Dockerfile`
- `apps/communication/Dockerfile`
- `apps/document-engine/Dockerfile`
- `apps/auth/Dockerfile`
- `apps/case-management/Dockerfile`

These Dockerfiles:
1. Build the application in a build stage
2. Run tests as a build-time gate
3. Create a minimal production image
4. Include health checks
5. Automatically detect and use the appropriate start script

## Required GitHub Secrets

The following secrets need to be configured in GitHub:

- `AWS_ACCESS_KEY_ID`: AWS access key with permissions to push to ECR
- `AWS_SECRET_ACCESS_KEY`: AWS secret key
- `AWS_REGION`: AWS region where ECR repositories are located
- `AWS_ACCOUNT_ID`: AWS account ID

## Workflow Customization

### Triggering a Build Manually

You can manually trigger a build for a specific service using the GitHub Actions workflow dispatch feature:

1. Go to the "Actions" tab in GitHub
2. Select the "Microservice CI/CD Pipeline" workflow
3. Click "Run workflow"
4. Enter the name of the service you want to build (leave empty to auto-detect changes)
5. Click "Run workflow"

### Adding a New Service

To add a new service to the CI/CD pipeline:

1. Create a new directory in the `apps` directory
2. Add a Dockerfile for the new service
3. Update the `docker-compose.services.yml` file to include the new service
4. The CI/CD pipeline will automatically detect the new service

## Troubleshooting

### Common Issues

- **Docker build fails**: Check the Dockerfile and ensure all dependencies are correctly specified
- **Tests fail during build**: Fix the failing tests before pushing changes
- **ECR push fails**: Ensure AWS credentials are correctly configured
- **Service doesn't start**: Check the logs using `docker-compose logs <service-name>`

### Viewing Logs

To view logs for a specific service:

```bash
docker-compose -f docker-compose.services.yml logs <service-name>
```

To follow logs in real-time:

```bash
docker-compose -f docker-compose.services.yml logs -f <service-name>
```
