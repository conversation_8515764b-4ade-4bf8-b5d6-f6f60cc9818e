# Configuration System for tk-lpm-backend

This document provides instructions on how to use the new configuration system implemented in the tk-lpm-backend project.

## Overview

The configuration system is built using `@nestjs/config` and provides a type-safe, modular, and environment-aware way to manage configuration across all microservices.

## Key Features

- Environment-specific configuration (development, production, test)
- Strict validation with Joi
- Type safety with TypeScript interfaces
- Modular and extensible design
- Global access to configuration across services

## Directory Structure

```
libs/common/src/config/
├── interfaces/                  # Type interfaces for configuration
│   ├── app.config.interface.ts
│   ├── auth.config.interface.ts
│   ├── database.config.interface.ts
│   ├── service.config.interface.ts
│   └── index.ts
├── app.config.ts               # Application configuration
├── auth.config.ts              # Authentication configuration
├── database.config.ts          # Database configuration
├── service.config.ts           # Microservices configuration
├── config.module.ts            # NestJS module for configuration
├── configuration.ts            # Main configuration factory
└── validation.schema.ts        # Joi validation schema
```

## Environment Variables

The system loads environment variables from the following files (in order of precedence):

1. `.env.{NODE_ENV}.local`
2. `.env.{NODE_ENV}`
3. `.env.local`
4. `.env`

## Usage

### Accessing Configuration in a Service

```typescript
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class YourService {
  constructor(private configService: ConfigService) {}

  someMethod() {
    // Access app configuration
    const appConfig = this.configService.get('app');
    console.log(`Running in ${appConfig.nodeEnv} mode`);

    // Access database configuration
    const dbConfig = this.configService.get('database');
    console.log(`Database host: ${dbConfig.host}`);

    // Access service configuration
    const serviceConfig = this.configService.get('service.core');
    console.log(`Core service port: ${serviceConfig.port}`);

    // Access auth configuration
    const authConfig = this.configService.get('auth');
    console.log(`JWT expiration: ${authConfig.jwtExpiration}`);
  }
}
```

### Using the Database Module

The database module is pre-configured to use the configuration system:

```typescript
import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/common';

@Module({
  imports: [DatabaseModule],
  // ...
})
export class YourModule {}
```

## Docker Compose

The project includes a Docker Compose file for PostgreSQL:

```bash
# Start PostgreSQL
docker-compose up -d postgres

# Stop PostgreSQL
docker-compose down

# View logs
docker-compose logs -f postgres
```

## Adding New Configuration

To add a new configuration section:

1. Create a new interface in `libs/common/src/config/interfaces/`
2. Create a new configuration file in `libs/common/src/config/`
3. Add the new configuration to `configuration.ts`
4. Update the validation schema in `validation.schema.ts`
5. Export the new configuration in `libs/common/src/index.ts`

## Validation

The configuration system validates all environment variables at startup. If validation fails, the application will not start and will display detailed error messages.

## Best Practices

1. Always use the ConfigService to access configuration values
2. Use type-safe interfaces for all configuration
3. Add validation for all environment variables
4. Keep sensitive information in environment variables, not in code
5. Use environment-specific configuration files for different environments
