import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Config } from '@app/common';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);
    app.setGlobalPrefix(Config.DOCUMENT_ENGINE_PREFIX);
    await app.listen(Config.DOCUMENT_ENGINE_PORT!);
    Logger.log(`Document Engine microservice running on ${await app.getUrl()}`);
}
bootstrap();
