# Auth Module API Endpoints

This document provides a summary of all available endpoints in the Auth Module.

## Base URL

The base URL for all endpoints is: `http://localhost:3003/api/auth`

## Authentication

Most endpoints require authentication via a JWT token. The token should be included in the `Authorization` header as a Bearer token:

```
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJfX...
```

## Tenant Management

### Create Tenant
- **Method**: POST
- **Path**: `/create-tenant`
- **Auth Required**: No
- **Description**: Creates a new tenant (realm) with an initial admin user
- **Request Body**:
  ```json
  {
    "realm": "example-tenant",
    "displayName": "Example Tenant",
    "adminUsername": "admin",
    "adminEmail": "<EMAIL>",
    "adminPassword": "Admin123!",
    "adminFirstName": "Admin",
    "adminLastName": "User",
    "registrationAllowed": true,
    "verifyEmail": true,
    "rememberMe": true
  }
  ```

## Authentication

### Login
- **Method**: POST
- **Path**: `/login`
- **Auth Required**: No
- **Description**: Authenticates a user and returns tokens
- **Request Body**:
  ```json
  {
    "username": "admin",
    "password": "Admin123!",
    "rememberMe": true,
    "realm": "example-tenant"
  }
  ```
- **Notes**: Sets cookies for access_token, refresh_token, and realm

### Refresh Token
- **Method**: POST
- **Path**: `/refresh`
- **Auth Required**: No
- **Description**: Refreshes tokens using a refresh token
- **Request Body**:
  ```json
  {
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "realm": "example-tenant"
  }
  ```
- **Notes**: The refresh token can be provided in the request body or in a cookie

### Logout
- **Method**: POST
- **Path**: `/logout`
- **Auth Required**: No
- **Description**: Logs out a user by clearing auth cookies
- **Notes**: Clears access_token, refresh_token, and realm cookies

## User Management

### Create User
- **Method**: POST
- **Path**: `/users`
- **Auth Required**: Yes
- **Description**: Creates a new user
- **Request Body**:
  ```json
  {
    "username": "newuser",
    "email": "<EMAIL>",
    "firstName": "New",
    "lastName": "User",
    "password": "Password123!",
    "enabled": true,
    "emailVerified": false,
    "temporaryPassword": true,
    "realm": "example-tenant"
  }
  ```

### Get User by ID
- **Method**: GET
- **Path**: `/users/:id`
- **Auth Required**: Yes
- **Description**: Gets a user by ID
- **Path Parameters**:
  - `id`: User ID
- **Headers**:
  - `x-realm`: Realm name (required if realm cookie is not set)

### Get Current User Profile
- **Method**: GET
- **Path**: `/me`
- **Auth Required**: Yes
- **Description**: Gets the current user's profile based on the JWT token

## Role Management

### Create Role
- **Method**: POST
- **Path**: `/roles`
- **Auth Required**: Yes
- **Description**: Creates a new role
- **Request Body**:
  ```json
  {
    "name": "manager",
    "description": "Manager role with limited admin privileges",
    "composite": false,
    "realm": "example-tenant"
  }
  ```

### Assign Roles to User
- **Method**: POST
- **Path**: `/users/:id/roles`
- **Auth Required**: Yes
- **Description**: Assigns roles to a user
- **Path Parameters**:
  - `id`: User ID
- **Request Body**:
  ```json
  {
    "roles": ["manager", "user"],
    "realm": "example-tenant"
  }
  ```
