import { registerAs } from '@nestjs/config';
import { DatabaseConfig, RawDatabaseEnv } from './interfaces';
import { DataSourceOptions } from 'typeorm';
import { PUBLIC_ENTITIES, TENANT_ENTITIES } from '../typeorm/entities';
import { PUBLIC_MIGRATIONS, TENANT_MIGRATIONS } from '../typeorm/migrations';

/**
 * Validates that all required environment variables are present
 * Throws an error if any required variable is missing
 * @param env Raw environment variables
 */
function validateDatabaseEnv(env: RawDatabaseEnv): void {
    // Define required environment variables
    const requiredVars: Array<keyof RawDatabaseEnv> = [
        'POSTGRES_HOST',
        'POSTGRES_PORT',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD',
        'POSTGRES_DB'
    ];

    // Check each required variable
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
    }
}

/**
 * Database configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const databaseConfig = registerAs('database', (): DatabaseConfig => {
    // Load raw environment variables
    const rawEnv: RawDatabaseEnv = {
        POSTGRES_HOST: process.env.POSTGRES_HOST,
        POSTGRES_PORT: process.env.POSTGRES_PORT,
        POSTGRES_USER: process.env.POSTGRES_USER,
        POSTGRES_PASSWORD: process.env.POSTGRES_PASSWORD
            ? String(process.env.POSTGRES_PASSWORD)
            : undefined,
        POSTGRES_DB: process.env.POSTGRES_DB,
        POSTGRES_SSL: process.env.POSTGRES_SSL,
        POSTGRES_MAX_CONNECTIONS: process.env.POSTGRES_MAX_CONNECTIONS,
        POSTGRES_IDLE_TIMEOUT_MS: process.env.POSTGRES_IDLE_TIMEOUT_MS,
        POSTGRES_CONNECTION_TIMEOUT_MS: process.env.POSTGRES_CONNECTION_TIMEOUT_MS
    };

    // Validate environment variables
    validateDatabaseEnv(rawEnv);

    // After validation, we can safely assert that required variables are defined
    return {
        host: rawEnv.POSTGRES_HOST!,
        port: parseInt(rawEnv.POSTGRES_PORT!, 10),
        username: rawEnv.POSTGRES_USER!,
        password: String(rawEnv.POSTGRES_PASSWORD!),
        database: rawEnv.POSTGRES_DB!,
        ssl: rawEnv.POSTGRES_SSL === 'true',
        maxConnections: parseInt(rawEnv.POSTGRES_MAX_CONNECTIONS || '100', 10),
        idleTimeoutMs: parseInt(rawEnv.POSTGRES_IDLE_TIMEOUT_MS || '30000', 10),
        connectionTimeoutMs: parseInt(rawEnv.POSTGRES_CONNECTION_TIMEOUT_MS || '2000', 10)
    };
});

/**
 * Gets the DataSource options for the public schema
 * Uses the validated database configuration
 */
export const getPublicDataSourceOptions = (): DataSourceOptions => {
    const config = databaseConfig();
    // Ensure password is a primitive string
    const password = String(config.password || '');

    return {
        type: 'postgres',
        host: config.host,
        port: config.port,
        username: config.username,
        password,
        database: config.database,
        schema: 'public',
        entities: PUBLIC_ENTITIES,
        migrations: PUBLIC_MIGRATIONS,
        synchronize: false,
        logging: process.env.NODE_ENV !== 'production',
        ssl: config.ssl,
        extra: {
            max: config.maxConnections,
            idleTimeoutMillis: config.idleTimeoutMs,
            connectionTimeoutMillis: config.connectionTimeoutMs
        }
    };
};

/**
 * Gets the DataSource options for a tenant schema
 * Uses the validated database configuration
 * @param schema The tenant schema name
 */
export const getTenantDataSourceOptions = (schema: string): DataSourceOptions => {
    const config = databaseConfig();

    // Ensure password is a primitive string
    const password = String(config.password || '');

    return {
        type: 'postgres',
        host: config.host,
        port: config.port,
        username: config.username,
        password,
        database: config.database,
        schema,
        entities: TENANT_ENTITIES,
        migrations: TENANT_MIGRATIONS,
        synchronize: false,
        logging: process.env.NODE_ENV !== 'production',
        ssl: config.ssl,
        extra: {
            max: config.maxConnections,
            idleTimeoutMillis: config.idleTimeoutMs,
            connectionTimeoutMillis: config.connectionTimeoutMs
        }
    };
};
