import { forwardRef, Module } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { PublicUserRepository } from './public-user.repository';
import { SystemRoleRepository } from './system-role.repository';
import { TenantRoleRepository } from './tenant-role.repository';
import { RoleRepository } from './role.repository';
import { TenantRepository } from './tenant.repository';
import { CommonModule } from '../common.module';

@Module({
    imports: [forwardRef(() => CommonModule)],
    providers: [
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantRepository
    ],
    exports: [
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantRepository
    ]
})
export class RepositoriesModule {}
