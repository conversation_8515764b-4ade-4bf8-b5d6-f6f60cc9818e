import { Injectable, Logger } from '@nestjs/common';
import { CaseRepository } from '../repositories/case.repository';
import { CaseAssignmentRepository } from '../repositories/case-assignment.repository';
import { CaseAuditService } from './case-audit.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';

/**
 * Service for handling case notifications
 */
@Injectable()
export class CaseNotificationService {
    private readonly logger = new Logger(CaseNotificationService.name);

    constructor(
        private readonly caseRepository: CaseRepository,
        private readonly caseAssignmentRepository: CaseAssignmentRepository,
        private readonly caseAuditService: CaseAuditService
    ) {}

    /**
     * Checks for upcoming deadlines and returns cases with deadlines within the specified hours
     * @param hours Number of hours to check for upcoming deadlines
     * @returns Cases with upcoming deadlines
     */
    async checkUpcomingDeadlines(hours: number = 24): Promise<any[]> {
        const repository = await this.caseRepository.getRepository();

        // Calculate the date range for upcoming deadlines
        const now = new Date();
        const futureDate = new Date(now.getTime() + hours * 60 * 60 * 1000);

        // Find cases with deadlines in the specified range
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .where('case.deadline IS NOT NULL')
            .andWhere('case.deadline > :now', { now })
            .andWhere('case.deadline <= :futureDate', { futureDate })
            .orderBy('case.deadline', 'ASC');

        const cases = await queryBuilder.getMany();

        // For each case, get the assignments
        const casesWithAssignments = await Promise.all(
            cases.map(async (caseEntity) => {
                const assignments = await this.caseAssignmentRepository.findByCaseId(caseEntity.id);
                return {
                    ...caseEntity,
                    assignments
                };
            })
        );

        return casesWithAssignments;
    }

    /**
     * Checks for missed deadlines and returns cases with deadlines that have passed
     * @returns Cases with missed deadlines
     */
    async checkMissedDeadlines(): Promise<any[]> {
        const repository = await this.caseRepository.getRepository();

        // Calculate the date range for missed deadlines
        const now = new Date();

        // Find cases with deadlines that have passed
        const queryBuilder = repository
            .createQueryBuilder('case')
            .leftJoinAndSelect('case.client', 'client')
            .where('case.deadline IS NOT NULL')
            .andWhere('case.deadline <= :now', { now })
            .orderBy('case.deadline', 'DESC');

        const cases = await queryBuilder.getMany();

        // For each case, get the assignments
        const casesWithAssignments = await Promise.all(
            cases.map(async (caseEntity) => {
                const assignments = await this.caseAssignmentRepository.findByCaseId(caseEntity.id);
                return {
                    ...caseEntity,
                    assignments
                };
            })
        );

        return casesWithAssignments;
    }

    /**
     * Sets a custom reminder for a case deadline
     * @param caseId The case ID
     * @param userId The user ID setting the reminder
     * @param reminderTime The time for the reminder (ISO string)
     * @param reminderType The type of reminder (e.g., email, in-app)
     * @returns The created reminder
     */
    async setCustomReminder(
        caseId: string,
        userId: string,
        userName: string,
        reminderTime: string,
        reminderType: string = 'email'
    ): Promise<any> {
        // In a real implementation, this would create a record in a reminders table
        // For now, we'll just log it in the audit trail

        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.REMINDER_SET,
            userId,
            userName,
            'system',
            {
                reminderTime,
                reminderType,
                setAt: new Date().toISOString()
            }
        );

        return {
            caseId,
            userId,
            reminderTime,
            reminderType,
            status: 'scheduled'
        };
    }
}
