import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException,
    Logger
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RoleHierarchyService } from '@app/common/roles/hierarchy';
import {
    ROLE_GROUP_KEY,
    ROLE_GROUP_ADMIN_KEY,
    ROLE_GROUP_USER_KEY,
    REQUIRE_ROLE_GROUP_ADMIN_KEY
} from '../permissions/role-group.decorators';

/**
 * Guard that handles role group-based access control
 * Works with the new role group system where roles are {roleGroup}_admin or {roleGroup}_user
 */
@Injectable()
export class RolesGuard implements CanActivate {
    private readonly logger = new Logger(RolesGuard.name);

    constructor(
        private readonly reflector: Reflector,
        private readonly roleHierarchyService: RoleHierarchyService
    ) {}

    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user || !user.roles) {
            this.logger.error('User information not available in request');
            throw new ForbiddenException('User information not available');
        }

        // Expand roles to include inherited roles
        const expandedRoles = this.roleHierarchyService.expandRoles(user.roles);
        
        this.logger.debug(`User roles: ${user.roles.join(', ')}`);
        this.logger.debug(`Expanded roles: ${expandedRoles.join(', ')}`);

        // Check for Super Admin (has access to everything)
        if (this.roleHierarchyService.isSuperAdmin(expandedRoles)) {
            this.logger.debug('User is Super Admin, granting access');
            return true;
        }

        // Get role group metadata
        const requiredRoleGroup = this.reflector.get<string>(ROLE_GROUP_KEY, context.getHandler());
        const requiredRoleGroupAdmin = this.reflector.get<string>(ROLE_GROUP_ADMIN_KEY, context.getHandler());
        const requiredRoleGroupUser = this.reflector.get<string>(ROLE_GROUP_USER_KEY, context.getHandler());
        const requireAnyRoleGroupAdmin = this.reflector.get<boolean>(REQUIRE_ROLE_GROUP_ADMIN_KEY, context.getHandler());
        const allowedRoleGroups = this.reflector.get<string[]>('allowedRoleGroups', context.getHandler());
        const allowedRoleGroupAdmins = this.reflector.get<string[]>('allowedRoleGroupAdmins', context.getHandler());

        // Check specific role group access (admin or user)
        if (requiredRoleGroup) {
            if (this.roleHierarchyService.belongsToRoleGroup(expandedRoles, requiredRoleGroup)) {
                this.logger.debug(`User belongs to role group: ${requiredRoleGroup}`);
                return true;
            }
            this.logger.warn(`User does not belong to required role group: ${requiredRoleGroup}`);
            throw new ForbiddenException(`Access denied: requires ${requiredRoleGroup} role group access`);
        }

        // Check specific role group admin access
        if (requiredRoleGroupAdmin) {
            if (this.roleHierarchyService.isAdminOfRoleGroup(expandedRoles, requiredRoleGroupAdmin)) {
                this.logger.debug(`User is admin of role group: ${requiredRoleGroupAdmin}`);
                return true;
            }
            this.logger.warn(`User is not admin of required role group: ${requiredRoleGroupAdmin}`);
            throw new ForbiddenException(`Access denied: requires ${requiredRoleGroupAdmin} admin access`);
        }

        // Check specific role group user access (includes admin)
        if (requiredRoleGroupUser) {
            if (this.roleHierarchyService.belongsToRoleGroup(expandedRoles, requiredRoleGroupUser)) {
                this.logger.debug(`User has access to role group: ${requiredRoleGroupUser}`);
                return true;
            }
            this.logger.warn(`User does not have access to role group: ${requiredRoleGroupUser}`);
            throw new ForbiddenException(`Access denied: requires ${requiredRoleGroupUser} access`);
        }

        // Check if user is admin of any role group
        if (requireAnyRoleGroupAdmin) {
            if (this.roleHierarchyService.isRoleGroupAdmin(expandedRoles)) {
                this.logger.debug('User is admin of at least one role group');
                return true;
            }
            this.logger.warn('User is not admin of any role group');
            throw new ForbiddenException('Access denied: requires role group admin access');
        }

        // Check multiple allowed role groups
        if (allowedRoleGroups && allowedRoleGroups.length > 0) {
            for (const roleGroup of allowedRoleGroups) {
                if (this.roleHierarchyService.belongsToRoleGroup(expandedRoles, roleGroup)) {
                    this.logger.debug(`User belongs to allowed role group: ${roleGroup}`);
                    return true;
                }
            }
            this.logger.warn(`User does not belong to any allowed role groups: ${allowedRoleGroups.join(', ')}`);
            throw new ForbiddenException(`Access denied: requires access to one of: ${allowedRoleGroups.join(', ')}`);
        }

        // Check multiple allowed role group admins
        if (allowedRoleGroupAdmins && allowedRoleGroupAdmins.length > 0) {
            for (const roleGroup of allowedRoleGroupAdmins) {
                if (this.roleHierarchyService.isAdminOfRoleGroup(expandedRoles, roleGroup)) {
                    this.logger.debug(`User is admin of allowed role group: ${roleGroup}`);
                    return true;
                }
            }
            this.logger.warn(`User is not admin of any allowed role groups: ${allowedRoleGroupAdmins.join(', ')}`);
            throw new ForbiddenException(`Access denied: requires admin access to one of: ${allowedRoleGroupAdmins.join(', ')}`);
        }

        // If no role group metadata is found, allow access (other guards will handle it)
        this.logger.debug('No role group metadata found, allowing access');
        return true;
    }
}
