import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
    Logger
} from '@nestjs/common';
import { AuthService } from 'apps/auth/src/services/auth.service';
import { Request } from 'express';
import { TenantRoleRepository } from '@app/common/repositories/tenant-role.repository';
import { TenantRepository } from '@app/common/repositories/tenant.repository';
import { ROLE_GROUPS } from '@app/common/permissions/role-group-permissions.defaults';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';

/**
 * Guard that validates JWT tokens using Keycloak's public key
 */
@Injectable()
export class JwtGuard implements CanActivate {
    private readonly logger = new Logger(JwtGuard.name);

    constructor(
        private readonly authService: AuthService,
        private readonly tenantRoleRepository: TenantRoleRepository,
        private readonly tenantRepository: TenantRepository,
        private readonly tenantContextService: TenantContextService
    ) {}

    /**
     * Validates the JWT token in the request
     */
    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<Request>();
        const token = this.extractTokenFromRequest(request);
        const realm = this.extractRealmFromRequest(request);

        if (!token) {
            throw new UnauthorizedException('No token provided');
        }

        if (!realm) {
            throw new UnauthorizedException('No realm provided');
        }

        try {
            // Verify the token using the public key from JWKS
            const decodedToken = await this.authService.verifyToken(token, realm);
            // Log the raw realm and resource access roles for debugging
            if (decodedToken.realm_access) {
                this.logger.debug(
                    `Raw realm_access roles: ${JSON.stringify(decodedToken.realm_access.roles || [])}`
                );
            }
            if (decodedToken.resource_access && typeof decodedToken.resource_access === 'object') {
                Object.keys(decodedToken.resource_access).forEach((client) => {
                    const clientRoles = decodedToken.resource_access?.[client]?.roles || [];
                    this.logger.debug(
                        `Raw resource_access for client ${client}: ${JSON.stringify(clientRoles)}`
                    );
                });
            }

            const roles = this.authService.extractRoles(decodedToken);
            this.logger.debug(`JWT Guard extracted roles: ${JSON.stringify(roles)}`);

            decodedToken.roles = roles;

            const user = await this.authService.getUserByEmail(decodedToken.email);

            // --- ENHANCEMENT: Attach all role group info ---
            let roleGroups: { key: string; isAdmin: boolean; roleId: string }[] = [];
            let isSuperAdmin = false;
            let primaryRoleGroupKey: string | null = null;
            let primaryRoleGroupId: string | null = null;
            let isGroupAdmin = false;
            if (user) {
                const tenantId = request.headers['x-tenant-id'] || decodedToken.tenantId;
                if (tenantId) {
                    // Set tenant context before making tenant-specific database operations
                    await this.setTenantContext(tenantId as string, realm);

                    const tenantRoles = await this.tenantRoleRepository.getUserRoles(user.id);
                    for (const role of tenantRoles) {
                        // Match against all role group keys
                        for (const group of ROLE_GROUPS) {
                            if (role.name === `${group.key}_user`) {
                                roleGroups.push({
                                    key: group.key,
                                    isAdmin: false,
                                    roleId: role.id
                                });
                            }
                            if (role.name === `${group.key}_admin`) {
                                roleGroups.push({ key: group.key, isAdmin: true, roleId: role.id });
                            }
                        }
                    }
                    // Set primary group as the first found
                    if (roleGroups.length > 0) {
                        primaryRoleGroupKey = roleGroups[0].key ?? null;
                        primaryRoleGroupId = roleGroups[0].roleId ?? null;
                        isGroupAdmin = roleGroups[0].isAdmin;
                    }
                }
                isSuperAdmin = roles.includes('SUPER_ADMIN');
            }
            request['user'] = {
                systemUserId: user?.id,
                ...decodedToken,
                roleGroups, // all group assignments
                roleGroupKey: primaryRoleGroupKey, // first group
                roleGroupId: primaryRoleGroupId, // first group id
                isGroupAdmin,
                isSuperAdmin
            };

            // Log the user object with roles to ensure they're properly attached
            this.logger.debug(
                `JWT Guard attached user: ${decodedToken.preferred_username || decodedToken.sub}`
            );
            this.logger.debug(`JWT Guard attached roles: ${JSON.stringify(decodedToken.roles)}`);

            return true;
        } catch (error) {
            this.logger.error(`Token validation failed: ${error.message}`, error.stack);
            throw new UnauthorizedException('Invalid token');
        }
    }

    /**
     * Extracts the token from the request
     * Checks Authorization header first, then cookies
     */
    private extractTokenFromRequest(request: Request): string | null {
        // Check Authorization header
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }

        // Check cookies
        if (request.cookies && request.cookies.access_token) {
            return request.cookies.access_token;
        }

        return null;
    }

    /**
     * Extracts the realm from the request
     * Checks custom header first, then cookies, then query params
     */
    private extractRealmFromRequest(request: Request): string | null {
        // Check custom header
        const realmHeader = request.headers['x-realm'] as string;
        if (realmHeader) {
            return realmHeader;
        }

        // Check cookies
        if (request.cookies && request.cookies.realm) {
            return request.cookies.realm;
        }

        // Check query params
        if (request.query && request.query.realm) {
            return request.query.realm as string;
        }

        return null;
    }

    /**
     * Sets the tenant context for database operations
     * This is crucial before making any tenant-specific database queries
     */
    private async setTenantContext(_tenantId: string, realm: string): Promise<void> {
        try {
            // Get tenant information from tenant repository
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (tenant) {
                // Set the tenant context for subsequent database operations
                this.tenantContextService.setTenant(tenant.id, {
                    id: tenant.id,
                    realm: tenant.realm,
                    displayName: tenant.displayName || `${tenant.adminFirstName}-${tenant.realm}`,
                    enabled: tenant.enabled
                });
                this.logger.debug(`Tenant context set for tenant: ${tenant.id} (realm: ${realm})`);
            } else {
                this.logger.warn(`Tenant not found for realm: ${realm}`);
            }
        } catch (error) {
            this.logger.error(`Failed to set tenant context for realm ${realm}:`, error);
            // Don't throw here as we want to continue with authentication
            // The tenant-specific operations will fail gracefully if context is not set
        }
    }

}
