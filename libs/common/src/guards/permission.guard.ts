import {
    Injectable,
    CanActivate,
    ExecutionContext,
    ForbiddenException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { AuditService } from '../audit/audit.service';
import { RoleGroupPermissionService } from '../permissions/role-group-permission.service';
import { Permission, ResourcePermissions } from '../permissions/enums/permission.enum';
import {
    PERMISSIONS_KEY,
    ResourceType,
    PermissionAuditAction
} from '../permissions/permission.constants';

/**
 * Interface for user object in request
 */
interface AuthenticatedUser {
    id: string;
    roleGroupId: string;
    isGroupAdmin: boolean;
    isSuperAdmin: boolean;
    [key: string]: any;
}

/**
 * Interface for permission metadata
 */
interface PermissionMetadata {
    resource: ResourceType;
    action: Permission;
}

/**
 * Extended Request interface with user
 */
interface AuthenticatedRequest extends Request {
    user: AuthenticatedUser;
}

/**
 * PermissionGuard: Checks if user has required permission for resource/action.
 * - Reads @HasPermission, @IsSuperAdmin, @IsRoleGroupAdmin metadata
 * - Enforces Super Admin delete-only rule
 * - Delegates own-case check to ConveyancerCaseGuard if needed
 * - Logs all checks via AuditService
 */
@Injectable()
export class PermissionGuard implements CanActivate {
    constructor(
        private readonly reflector: Reflector,
        private readonly roleGroupPermissionService: RoleGroupPermissionService,
        private readonly auditService: AuditService
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const req = context.switchToHttp().getRequest<AuthenticatedRequest>();
        const user: AuthenticatedUser = req.user;
        const tenantId: string = req.headers['x-tenant-id'] as string;
        const roleGroupId: string = user.roleGroupId;
        const isSuperAdmin: boolean = user.isSuperAdmin;

        // Read permission metadata
        const permissionMeta = this.reflector.get<PermissionMetadata[]>(PERMISSIONS_KEY, context.getHandler());
        if (!permissionMeta) return true; // No permission required
        const { resource, action }: PermissionMetadata = permissionMeta[0];

        // Super Admin: Only allow delete for Case/Document
        if (isSuperAdmin) {
            if (action === Permission.DELETE && (resource === ResourceType.CASE || resource === ResourceType.DOCUMENT)) {
                await this.log(req, tenantId, user, resource, action, true);
                return true;
            }
            await this.log(req, tenantId, user, resource, action, false);
            throw new ForbiddenException('Only Super Admin can delete cases/documents');
        }

        // Role Group permission check
        const allowed = await this.roleGroupPermissionService.getAllowedActions(
            roleGroupId,
            resource
        );
        const isAllowed = allowed.has(action);
        await this.log(req, tenantId, user, resource, action, isAllowed);
        if (!isAllowed) throw new ForbiddenException('Insufficient permissions');
        return true;
    }

    private async log(
        req: AuthenticatedRequest,
        tenantId: string,
        user: AuthenticatedUser,
        resource: ResourceType,
        action: Permission,
        allowed: boolean
    ): Promise<void> {
        await this.auditService.logPermissionCheck({
            userId: user.id,
            tenantId,
            action: allowed
                ? PermissionAuditAction.PERMISSION_GRANTED
                : PermissionAuditAction.PERMISSION_DENIED,
            resourceType: resource.toUpperCase(),
            resourceId: req.params['id'] || req.params['caseId'] || 'unknown',
            timestamp: new Date().toISOString(),
            result: allowed
                ? PermissionAuditAction.PERMISSION_GRANTED
                : PermissionAuditAction.PERMISSION_DENIED,
            permissions: [ResourcePermissions.create(resource.toUpperCase(), action)],
            ipAddress: req.ip || 'unknown',
            userAgent: req.headers['user-agent'] || 'unknown'
        });
    }
}
