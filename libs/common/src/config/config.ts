import * as dotenv from 'dotenv';
import * as path from 'path';

// Loading .env file from the root of the project
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

export class Config {
    static readonly CORE_PORT = process.env.CORE_PORT;
    static readonly COMMUNICATION_PORT = process.env.COMMUNICATION_PORT;
    static readonly DOCUMENT_ENGINE_PORT = process.env.DOCUMENT_ENGINE_PORT;
    static readonly AUTH_PORT = process.env.AUTH_PORT;
    static readonly CASE_MANAGEMENT_PORT = process.env.CASE_MANAGEMENT_PORT;
    static readonly TASK_MANAGEMENT_PORT = process.env.TASK_MANAGEMENT_PORT;

    // API prefixes
    static readonly CORE_PREFIX = 'api';
    static readonly COMMUNICATION_PREFIX = 'api/communication';
    static readonly DOCUMENT_ENGINE_PREFIX = 'api/document-engine';
    static readonly AUTH_PREFIX = 'api/auth';
    static readonly CASE_MANAGEMENT_PREFIX = 'api/case-management';
    static readonly TASK_MANAGEMENT_PREFIX = 'api/task-management';

    static readonly NODE_ENV = process.env.NODE_ENV || 'development';
    static readonly IS_PRODUCTION = this.NODE_ENV === 'production';
    static readonly IS_DEVELOPMENT = this.NODE_ENV === 'development';
}
