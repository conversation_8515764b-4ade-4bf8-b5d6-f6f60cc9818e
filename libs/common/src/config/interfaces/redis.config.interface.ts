/**
 * Raw Redis environment variables interface
 * This represents the raw, possibly undefined environment variables
 */
export interface RawRedisEnv {
    REDIS_HOST?: string;
    REDIS_PORT?: string;
    REDIS_PASSWORD?: string;
    REDIS_DB?: string;
    REDIS_TLS?: string;
}

/**
 * Redis configuration interface
 * This represents the fully validated configuration
 */
export interface RedisConfig {
    host: string;
    port: number;
    password: string;
    db: number;
    tls: boolean;
}
