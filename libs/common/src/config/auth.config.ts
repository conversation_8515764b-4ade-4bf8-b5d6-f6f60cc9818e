import { registerAs } from '@nestjs/config';
import { AuthConfig, RawAuthEnv } from './interfaces';

/**
 * Validates that all required environment variables are present
 * Throws an error if any required variable is missing
 * @param env Raw environment variables
 */
function validateAuthEnv(env: RawAuthEnv): void {
    // Define required environment variables
    const requiredVars: Array<keyof RawAuthEnv> = [
        // NestJS API service Keycloak variables
        'KEYCLOAK_SERVER_URL',
        'KEYCLOAK_REALM',
        'KEYCLOAK_CLIENT_ID',
        'KEYCLOAK_CLIENT_SECRET',

        // Docker Keycloak container variables
        'KEYCLOAK_HOST',
        'KEY<PERSON>OAK_ADMIN',
        'KEYCLOAK_ADMIN_PASSWORD',
        'KEYCLOAK_HOSTNAME_PORT'
    ];

    // Check each required variable
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
    }
}

/**
 * Authentication configuration
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const authConfig = registerAs('auth', (): AuthConfig => {
    // Load raw environment variables
    const rawEnv: RawAuthEnv = {
        // NestJS API service Keycloak variables
        KEYCLOAK_SERVER_URL: process.env.KEYCLOAK_SERVER_URL,
        KEYCLOAK_REALM: process.env.KEYCLOAK_REALM,
        KEYCLOAK_CLIENT_ID: process.env.KEYCLOAK_CLIENT_ID,
        KEYCLOAK_CLIENT_SECRET: process.env.KEYCLOAK_CLIENT_SECRET,

        // Docker Keycloak container variables
        KEYCLOAK_HOST: process.env.KEYCLOAK_HOST,
        KEYCLOAK_ADMIN: process.env.KEYCLOAK_ADMIN,
        KEYCLOAK_ADMIN_PASSWORD: process.env.KEYCLOAK_ADMIN_PASSWORD,
        KEYCLOAK_HOSTNAME_PORT: process.env.KEYCLOAK_HOSTNAME_PORT
    };

    // Validate environment variables
    validateAuthEnv(rawEnv);

    // After validation, we can safely assert that required variables are defined
    return {
        // NestJS API service Keycloak configuration
        serverUrl: rawEnv.KEYCLOAK_SERVER_URL!,
        realm: rawEnv.KEYCLOAK_REALM!,
        clientId: rawEnv.KEYCLOAK_CLIENT_ID!,
        clientSecret: rawEnv.KEYCLOAK_CLIENT_SECRET!,

        // Docker Keycloak container configuration
        host: rawEnv.KEYCLOAK_HOST!,
        admin: rawEnv.KEYCLOAK_ADMIN!,
        adminPassword: rawEnv.KEYCLOAK_ADMIN_PASSWORD!,
        hostnamePort: parseInt(rawEnv.KEYCLOAK_HOSTNAME_PORT!, 10)
    };
});
