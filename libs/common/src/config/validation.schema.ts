import * as Joi from 'joi';

/**
 * Validation schema for environment variables
 * This ensures that all required environment variables are present and correctly typed
 */
export const validationSchema = Joi.object({
    // Environment
    NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),

    // Service Ports (required)
    CORE_PORT: Joi.number().port().required(),
    COMMUNICATION_PORT: Joi.number().port().required(),
    DOCUMENT_ENGINE_PORT: Joi.number().port().required(),
    AUTH_PORT: Joi.number().port().required(),
    CASE_MANAGEMENT_PORT: Joi.number().port().required(),

    // Database Configuration (required)
    POSTGRES_HOST: Joi.string().required(),
    POSTGRES_PORT: Joi.number().port().required(),
    POSTGRES_USER: Joi.string().required(),
    POSTGRES_PASSWORD: Joi.string().required(),
    POSTGRES_DB: Joi.string().required(),

    // Database Connection Options
    POSTGRES_SSL: Joi.boolean().default(false),
    POSTGRES_MAX_CONNECTIONS: Joi.number().default(100),
    POSTGRES_IDLE_TIMEOUT_MS: Joi.number().default(30000),
    POSTGRES_CONNECTION_TIMEOUT_MS: Joi.number().default(2000),

    // Redis configuration
    REDIS_HOST: Joi.string().required(),
    REDIS_PORT: Joi.number().required(),
    REDIS_PASSWORD: Joi.string().required(),
    REDIS_DB: Joi.number().required(),
    REDIS_TLS: Joi.boolean().default(false),

    // Cache TTLs
    CACHE_TTL_CASE_DETAILS: Joi.number().required(),
    CACHE_TTL_TASK_DETAILS: Joi.number().required(),
    CACHE_KEY_VERSION: Joi.string().default('v2'),

    // Keycloak Authentication - NestJS API service
    KEYCLOAK_SERVER_URL: Joi.string().required(),
    KEYCLOAK_REALM: Joi.string().required(),
    KEYCLOAK_CLIENT_ID: Joi.string().required(),
    KEYCLOAK_CLIENT_SECRET: Joi.string().required(),

    // Keycloak Authentication - Docker container
    KEYCLOAK_HOST: Joi.string().required(),
    KEYCLOAK_ADMIN: Joi.string().required(),
    KEYCLOAK_ADMIN_PASSWORD: Joi.string().required(),
    KEYCLOAK_HOSTNAME_PORT: Joi.number().port().required(),

    // API Configuration
    API_GLOBAL_PREFIX: Joi.string().default('api'),
    API_RATE_LIMIT_WINDOW_MS: Joi.number().default(60000), // 1 minute
    API_RATE_LIMIT_MAX: Joi.number().default(100), // 100 requests per minute

    // Logging
    LOG_LEVEL: Joi.string()
        .valid('error', 'warn', 'info', 'http', 'verbose', 'debug', 'silly')
        .default('info'),

    // CORS Configuration
    CORS_ENABLED: Joi.boolean().default(true),
    CORS_ORIGIN: Joi.string().default('*'),

    // Multi-tenancy Configuration
    MULTI_TENANCY_MAX_CONNECTIONS: Joi.number().default(50),
    MULTI_TENANCY_CONNECTION_TTL: Joi.number().default(3600000), // 1 hour
    MULTI_TENANCY_AUTO_CREATE_SCHEMA: Joi.boolean().default(true),
    MULTI_TENANCY_AUTO_RUN_MIGRATIONS: Joi.boolean().default(false),
    MULTI_TENANCY_MIGRATIONS_DIR: Joi.string().optional()

    // Cloud Secret Manager Placeholder
    // CLOUD_SECRET_MANAGER_ENABLED: Joi.boolean().default(false),
    // CLOUD_SECRET_MANAGER_TYPE: Joi.string().valid('aws', 'gcp', 'azure').optional(),
});
