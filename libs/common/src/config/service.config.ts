import { registerAs } from '@nestjs/config';
import { ServiceConfig, RawServiceEnv } from './interfaces';

/**
 * Validates that all required environment variables are present
 * Throws an error if any required variable is missing
 * @param env Raw environment variables
 */
function validateServiceEnv(env: RawServiceEnv): void {
    // Define required environment variables
    const requiredVars: Array<keyof RawServiceEnv> = [
        'CORE_PORT',
        'COMMUNICATION_PORT',
        'DOCUMENT_ENGINE_PORT',
        'AUTH_PORT',
        'CASE_MANAGEMENT_PORT',
        'TASK_MANAGEMENT_PORT'
    ];

    // Check each required variable
    for (const key of requiredVars) {
        if (env[key] === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
    }
}

/**
 * Service configuration for microservices
 * Loads raw environment variables, validates them, and returns a fully validated config
 */
export const serviceConfig = registerAs('service', (): ServiceConfig => {
    // Load raw environment variables
    const rawEnv: RawServiceEnv = {
        CORE_PORT: process.env.CORE_PORT,
        COMMUNICATION_PORT: process.env.COMMUNICATION_PORT,
        DOCUMENT_ENGINE_PORT: process.env.DOCUMENT_ENGINE_PORT,
        AUTH_PORT: process.env.AUTH_PORT,
        CASE_MANAGEMENT_PORT: process.env.CASE_MANAGEMENT_PORT,
        TASK_MANAGEMENT_PORT: process.env.TASK_MANAGEMENT_PORT
    };

    // Validate environment variables
    validateServiceEnv(rawEnv);

    // After validation, we can safely assert that required variables are defined
    return {
        core: {
            port: parseInt(rawEnv.CORE_PORT!, 10),
            prefix: 'api'
        },
        communication: {
            port: parseInt(rawEnv.COMMUNICATION_PORT!, 10),
            prefix: 'api/communication'
        },
        documentEngine: {
            port: parseInt(rawEnv.DOCUMENT_ENGINE_PORT!, 10),
            prefix: 'api/document-engine'
        },
        auth: {
            port: parseInt(rawEnv.AUTH_PORT!, 10),
            prefix: 'api/auth'
        },
        caseManagement: {
            port: parseInt(rawEnv.CASE_MANAGEMENT_PORT!, 10),
            prefix: 'api/case-management'
        },
        taskManagement: {
            port: parseInt(rawEnv.TASK_MANAGEMENT_PORT!, 10),
            prefix: 'api/task-management'
        }
    };
});
