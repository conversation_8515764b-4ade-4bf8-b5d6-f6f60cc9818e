export enum AppRole {
    USER = 'USER',
    MANAGER = 'MANAGER',
    ADMIN = 'ADMIN',
    SUPER_ADMIN = 'SUPER_ADMIN',
    LAWYER = 'LAWYER',
    FINANCE = 'FINANCE',
    PARTNER = 'PARTNER',
    SUPERVISOR = 'SUPERVISOR'
}

// Role descriptions for UI display
export const RoleDescriptions: Record<string, string> = {
    [AppRole.USER]: 'Regular User',
    [AppRole.MANAGER]: 'Manager',
    [AppRole.ADMIN]: 'Tenant Administrator',
    [AppRole.SUPER_ADMIN]: 'System Administrator',
    [AppRole.LAWYER]: 'Lawyer Administrator',
    [AppRole.FINANCE]: 'Finance Administrator',
    [AppRole.PARTNER]: 'Partner Administrator',
    [AppRole.SUPERVISOR]: 'Case Supervisor'
};
