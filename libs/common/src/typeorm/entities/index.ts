// Public schema entities
import { SystemUser } from './public/system-user.entity';
import { SystemRole } from './public/system-role.entity';
import { Tenant } from './public/tenant.entity';

// Tenant schema entities
import { UserProfile } from './tenant/user-profile.entity';
import { TenantRole } from './tenant/tenant-role.entity';

import { CaseAssignment } from './tenant/case-assignment.entity';
import { CaseAttachment } from './tenant/case-attachment.entity';
import { CaseAudit } from './tenant/case-audit.entity';
import { CaseContact } from './tenant/case-contact.entity';
import { CaseEvent } from './tenant/case-event.entity';
import { CaseNote } from './tenant/case-note.entity';
import { CaseRelation } from './tenant/case-relation.entity';
import { Case } from './tenant/case.entity';
import { Client } from './tenant/client.entity';
import { Task } from './tenant/task.entity';
import { TaskDependency } from './tenant/task-dependency.entity';
import { TaskHistory } from './tenant/task-history.entity';

// Document entities
import { DocumentFolder } from './tenant/document-folder.entity';
import { Document } from './tenant/document.entity';
import { DocumentAccess } from './tenant/document-access.entity';
import { DocumentAudit } from './tenant/document-audit.entity';
import { DocumentWorkflow } from './tenant/document-workflow.entity';

export const PUBLIC_ENTITIES = [SystemUser, SystemRole, Tenant];

export const TENANT_ENTITIES = [
    UserProfile,
    TenantRole,
    Case,
    Client,
    CaseAssignment,
    CaseNote,
    CaseAttachment,
    CaseAudit,
    CaseContact,
    CaseEvent,
    CaseRelation,
    Task,
    TaskDependency,
    TaskHistory,
    DocumentFolder,
    Document,
    DocumentAccess,
    DocumentAudit,
    DocumentWorkflow
];

export const ALL_ENTITIES = [...PUBLIC_ENTITIES, ...TENANT_ENTITIES];

export {
    SystemUser,
    SystemRole,
    Tenant,
    UserProfile,
    TenantRole,
    Case,
    Client,
    CaseAssignment,
    CaseNote,
    CaseAttachment,
    CaseAudit,
    CaseContact,
    CaseEvent,
    CaseRelation,
    Task,
    TaskDependency,
    TaskHistory,
    DocumentFolder,
    Document,
    DocumentAccess,
    DocumentAudit,
    DocumentWorkflow
};
