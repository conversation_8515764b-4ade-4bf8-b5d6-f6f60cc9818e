import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';

/**
 * CaseNote entity representing a note attached to a case
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('case_notes')
export class CaseNote {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case, (caseEntity) => caseEntity.notes)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ type: 'text' })
    content: string;

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_by_name', nullable: true })
    createdByName: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'is_pinned', default: false })
    isPinned: boolean;

    @Column({ name: 'is_private', default: false })
    isPrivate: boolean;
}
