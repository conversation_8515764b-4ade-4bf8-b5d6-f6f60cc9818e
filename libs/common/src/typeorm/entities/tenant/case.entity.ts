import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { CaseAssignment } from './case-assignment.entity';
import { CaseNote } from './case-note.entity';
import { CaseAttachment } from './case-attachment.entity';
import { CaseAudit } from './case-audit.entity';
import { CaseContact } from './case-contact.entity';
import { CaseEvent } from './case-event.entity';
import { Client } from './client.entity';
import { CaseStatus } from '@app/common/enums/case-status.enum';

export enum CasePriority {
    LOW = 'LOW',
    MEDIUM = 'MEDIUM',
    HIGH = 'HIGH',
    URGENT = 'URGENT'
}

export enum CaseType {
    LITIGATION = 'LITIGATION',
    CORPORATE = 'CORPORATE',
    REAL_ESTATE = 'REAL_ESTATE',
    INTELLECTUAL_PROPERTY = 'INTELLECTUAL_PROPERTY',
    FAMILY = 'FAMILY',
    CRIMINAL = 'CRIMINAL',
    OTHER = 'OTHER'
}

/**
 * Case entity representing a legal case in the system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('cases')
export class Case {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ name: 'case_number', unique: true })
    caseNumber: string;

    @Column()
    title: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: CaseStatus,
        default: CaseStatus.DRAFT
    })
    status: CaseStatus;

    @Column({
        type: 'enum',
        enum: CasePriority,
        default: CasePriority.MEDIUM
    })
    priority: CasePriority;

    @Column({
        type: 'enum',
        enum: CaseType,
        default: CaseType.OTHER
    })
    type: CaseType;

    @Column({ name: 'client_id' })
    clientId: string;

    @ManyToOne(() => Client, (client) => client.cases)
    @JoinColumn({ name: 'client_id' })
    client: Client;

    @OneToMany(() => CaseAssignment, (assignment) => assignment.case)
    assignments: CaseAssignment[];

    @OneToMany(() => CaseNote, (note) => note.case)
    notes: CaseNote[];

    @OneToMany(() => CaseAttachment, (attachment) => attachment.case)
    attachments: CaseAttachment[];

    @OneToMany(() => CaseAudit, (audit) => audit.case)
    auditTrail: CaseAudit[];

    @OneToMany(() => CaseContact, (contact) => contact.case)
    contacts: CaseContact[];

    @OneToMany(() => CaseEvent, (event) => event.case)
    events: CaseEvent[];

    @Column({ name: 'created_by' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @Column({ name: 'deadline', type: 'timestamp', nullable: true })
    deadline: Date | null;
}
