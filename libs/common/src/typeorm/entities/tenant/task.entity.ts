import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { TenantEntity } from '@app/common/multi-tenancy/decorators/tenant-entity.decorator';
import { Case } from './case.entity';
import { TaskDependency } from './task-dependency.entity';
import { TaskHistory } from './task-history.entity';

/**
 * Enum for task status
 */
export enum TaskStatus {
    OPEN = 'OPEN',
    IN_PROGRESS = 'IN_PROGRESS',
    BLOCKED = 'BLOCKED',
    DONE = 'DONE'
}

/**
 * Enum for task priority (JIRA-like)
 */
export enum TaskPriority {
    HIGHEST = 'HIGHEST',
    HIGH = 'HIGH',
    MEDIUM = 'MEDIUM',
    LOW = 'LOW',
    LOWEST = 'LOWEST'
}

/**
 * Task entity representing a task in the system
 * This is a tenant-specific entity stored in the tenant's schema
 */
@TenantEntity()
@Entity('tasks')
export class Task {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({
        type: 'enum',
        enum: TaskStatus,
        enumName: 'task_status',
        default: TaskStatus.OPEN
    })
    status: TaskStatus;

    @Column({
        type: 'enum',
        enum: TaskPriority,
        enumName: 'task_priority',
        default: TaskPriority.MEDIUM
    })
    priority: TaskPriority;

    @Column({ name: 'due_date', type: 'timestamp', nullable: true })
    dueDate: Date | null;

    @Column({ name: 'case_id' })
    caseId: string;

    @ManyToOne(() => Case)
    @JoinColumn({ name: 'case_id' })
    case: Case;

    @Column({ name: 'assignee_id', type: 'varchar', nullable: true })
    assigneeId: string | null;

    @Column({ name: 'created_by', type: 'varchar' })
    createdBy: string;

    @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    updatedAt: Date;

    @OneToMany(() => TaskDependency, (dependency) => dependency.task)
    dependencies: TaskDependency[];

    @OneToMany(() => TaskDependency, (dependency) => dependency.dependsOn)
    dependents: TaskDependency[];

    @OneToMany(() => TaskHistory, (history) => history.task)
    statusHistory: TaskHistory[];
}
