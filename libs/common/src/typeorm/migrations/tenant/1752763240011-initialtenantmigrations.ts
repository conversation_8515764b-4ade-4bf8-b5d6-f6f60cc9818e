import { MigrationInterface, QueryRunner } from 'typeorm';

export class Initialtenantmigrations1752763240011 implements MigrationInterface {
    name = 'Initialtenantmigrations1752763240011';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "case_attachments_document_type_enum" AS ENUM('PLEADING', 'CONTRACT', 'EVIDENCE', 'CORRESPONDENCE', 'COURT_ORDER', 'INVOICE', 'MEMO', 'RESEARCH', 'OTHER')`);

        await queryRunner.query(`CREATE TYPE "case_audit_action_enum" AS ENUM('CREATED', 'UPDATED', 'CLIENT_UPDATED', 'STATUS_CHANGED', 'STATUS_DRAFT', 'STATUS_SUBMITTED', 'STATUS_APPROVED', 'STATUS_DECLINED', 'STATUS_IN_PROGRESS', 'STATUS_ON_HOLD', 'STATUS_CLOSED', 'STATUS_ARCHIVED', 'ASSIGNED', 'UNASSIGNED', 'REASSIGNED', 'NOTE_ADDED', 'NOTE_UPDATED', 'NOTE_DELETED', 'ATTACHMENT_ADDED', 'ATTACHMENT_UPDATED', 'ATTACHMENT_REMOVED', 'CONTACT_ADDED', 'CONTACT_UPDATED', 'CONTACT_DELETED', 'CASE_RELATION_ADDED', 'CASE_RELATION_DELETED', 'ACCESSED', 'ACCESS_DENIED', 'EXPORTED', 'CLIENT_CREATED', 'REMINDER_SET', 'DEADLINE_APPROACHING', 'DEADLINE_MISSED', 'STATUS_UNDER_REVIEW', 'STATUS_ASSIGNED', 'STATUS_PENDING_APPROVAL', 'STATUS_REJECTED', 'STATUS_RESOLVED', 'STATUS_REOPENED', 'PERMISSION_CHECK')`);

        await queryRunner.query(`CREATE TYPE "case_contacts_type_enum" AS ENUM('CLIENT', 'CO_COUNSEL', 'OPPOSING_COUNSEL', 'WITNESS', 'EXPERT', 'JUDGE', 'OTHER')`);

        await queryRunner.query(`CREATE TYPE "case_events_category_enum" AS ENUM('INTAKE', 'PLEADINGS', 'DISCOVERY', 'MOTIONS', 'HEARINGS', 'TRIAL', 'POST_TRIAL', 'DEADLINE', 'ADMINISTRATIVE', 'COMMUNICATION', 'OTHER')`);

        await queryRunner.query(`CREATE TYPE "case_events_type_enum" AS ENUM('INITIAL_CONSULTATION', 'CONFLICT_CHECK', 'ENGAGEMENT_LETTER', 'CASE_OPENED', 'STATUTE_OF_LIMITATIONS', 'COMPLAINT_FILED', 'SUMMONS_ISSUED', 'SUMMONS_SERVED', 'ANSWER_FILED', 'COUNTERCLAIM_FILED', 'CROSS_CLAIM_FILED', 'AMENDED_PLEADING', 'INTERROGATORIES_SERVED', 'INTERROGATORIES_ANSWERED', 'DOCUMENT_REQUEST_SERVED', 'DOCUMENT_PRODUCTION', 'DEPOSITION_SCHEDULED', 'DEPOSITION_COMPLETED', 'EXPERT_DISCLOSURE', 'DISCOVERY_CUTOFF', 'DISCOVERY_DISPUTE', 'MOTION_TO_COMPEL', 'MOTION_TO_DISMISS', 'SUMMARY_JUDGMENT_MOTION', 'MOTION_HEARING', 'MOTION_IN_LIMINE', 'CASE_MANAGEMENT_CONFERENCE', 'STATUS_CONFERENCE', 'SETTLEMENT_CONFERENCE', 'MEDIATION', 'PRE_TRIAL_CONFERENCE', 'TRIAL_START', 'TRIAL_END', 'WITNESS_LIST_FILED', 'EXHIBIT_LIST_FILED', 'JURY_SELECTION', 'OPENING_ARGUMENTS', 'CLOSING_ARGUMENTS', 'VERDICT', 'JUDGMENT_ENTERED', 'POST_TRIAL_MOTION', 'NOTICE_OF_APPEAL', 'APPELLATE_BRIEF', 'ORAL_ARGUMENTS', 'APPELLATE_DECISION', 'COURT_DEADLINE', 'INTERNAL_DEADLINE', 'RESPONSE_DEADLINE', 'COURT_FEE_PAYMENT', 'BILLING_MILESTONE', 'COMPLIANCE_AUDIT', 'CLIENT_MEETING', 'CLIENT_CALL', 'TEAM_MEETING', 'OPPOSING_COUNSEL_COMMUNICATION', 'COURT_COMMUNICATION', 'OTHER')`);

        await queryRunner.query(`CREATE TYPE "cases_status_enum" AS ENUM('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'ASSIGNED', 'IN_PROGRESS', 'ON_HOLD', 'PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'RESOLVED', 'CLOSED', 'REOPENED', 'ARCHIVED')`);

        await queryRunner.query(`CREATE TYPE "cases_priority_enum" AS ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT')`);

        await queryRunner.query(`CREATE TYPE "cases_type_enum" AS ENUM('LITIGATION', 'CORPORATE', 'REAL_ESTATE', 'INTELLECTUAL_PROPERTY', 'FAMILY', 'CRIMINAL', 'OTHER')`);

        await queryRunner.query(`CREATE TYPE "case_relations_type_enum" AS ENUM('PARENT', 'CHILD', 'RELATED', 'PREDECESSOR', 'SUCCESSOR')`);

        await queryRunner.query(`CREATE TYPE "task_status" AS ENUM('OPEN', 'IN_PROGRESS', 'BLOCKED', 'DONE')`);

        await queryRunner.query(`CREATE TYPE "task_priority" AS ENUM('HIGHEST', 'HIGH', 'MEDIUM', 'LOW', 'LOWEST')`);

        await queryRunner.query(`CREATE TABLE "tenant_roles" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" CHARACTER VARYING,
            "permissions" JSONB,
            "enabled" BOOLEAN NOT NULL DEFAULT true,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "UQ_7b9f6e7e" UNIQUE ("name")
        )`);

        await queryRunner.query(`CREATE TABLE "user_profiles" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "username" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING NOT NULL,
            "first_name" CHARACTER VARYING,
            "department" CHARACTER VARYING,
            "last_name" CHARACTER VARYING,
            "enabled" BOOLEAN NOT NULL DEFAULT true,
            "email_verified" BOOLEAN NOT NULL DEFAULT false,
            "keycloak_id" CHARACTER VARYING,
            "additional_info" JSONB DEFAULT '{}',
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "user_id" CHARACTER VARYING
        )`);

        await queryRunner.query(`CREATE TABLE "clients" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING,
            "phone" CHARACTER VARYING,
            "address" TEXT,
            "additional_info" JSONB,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now()
        )`);

        await queryRunner.query(`CREATE TABLE "cases" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_number" CHARACTER VARYING NOT NULL,
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "status" "cases_status_enum" NOT NULL DEFAULT 'DRAFT',
            "priority" "cases_priority_enum" NOT NULL DEFAULT 'MEDIUM',
            "type" "cases_type_enum" NOT NULL DEFAULT 'OTHER',
            "client_id" UUID NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "deadline" TIMESTAMP,
            CONSTRAINT "FK_64ab2bed" FOREIGN KEY ("client_id") REFERENCES "clients"("id"),
            CONSTRAINT "UQ_4271e43c" UNIQUE ("case_number")
        )`);

        await queryRunner.query(`CREATE TABLE "case_notes" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "content" TEXT NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "is_pinned" BOOLEAN NOT NULL DEFAULT false,
            "is_private" BOOLEAN NOT NULL DEFAULT false,
            CONSTRAINT "FK_217b863d" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_attachments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "filename" CHARACTER VARYING NOT NULL,
            "url" TEXT NOT NULL,
            "file_size" INTEGER,
            "mime_type" CHARACTER VARYING,
            "uploaded_by" CHARACTER VARYING NOT NULL,
            "uploaded_by_name" CHARACTER VARYING,
            "uploaded_at" TIMESTAMP NOT NULL DEFAULT now(),
            "description" TEXT,
            "document_type" "case_attachments_document_type_enum" NOT NULL DEFAULT 'OTHER',
            CONSTRAINT "FK_178c494" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_audit" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "action" "case_audit_action_enum" NOT NULL,
            "performed_by" CHARACTER VARYING NOT NULL,
            "performed_by_name" CHARACTER VARYING,
            "performed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "ip_address" CHARACTER VARYING,
            "details" JSONB,
            CONSTRAINT "FK_4a1d1957" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_contacts" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "name" CHARACTER VARYING NOT NULL,
            "email" CHARACTER VARYING,
            "phone" CHARACTER VARYING,
            "address" TEXT,
            "type" "case_contacts_type_enum" NOT NULL DEFAULT 'OTHER',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "additional_info" JSONB,
            CONSTRAINT "FK_4e3a99cd" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_events" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "category" "case_events_category_enum" NOT NULL DEFAULT 'OTHER',
            "type" "case_events_type_enum" NOT NULL DEFAULT 'OTHER',
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "event_date" TIMESTAMP NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "metadata" JSONB,
            CONSTRAINT "FK_5ea58bad" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_assignments" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "user_id" CHARACTER VARYING NOT NULL,
            "user_name" CHARACTER VARYING,
            "assigned_by" CHARACTER VARYING NOT NULL,
            "assigned_at" TIMESTAMP NOT NULL DEFAULT now(),
            "is_active" BOOLEAN NOT NULL DEFAULT true,
            "notes" TEXT,
            CONSTRAINT "FK_578532fe" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "case_relations" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "case_id" UUID NOT NULL,
            "related_case_id" UUID NOT NULL,
            "type" "case_relations_type_enum" NOT NULL DEFAULT 'RELATED',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_by_name" CHARACTER VARYING,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "notes" TEXT,
            CONSTRAINT "FK_61dae6f3" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_5ac055df" FOREIGN KEY ("related_case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "tasks" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "title" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "status" "task_status" NOT NULL DEFAULT 'OPEN',
            "priority" "task_priority" NOT NULL DEFAULT 'MEDIUM',
            "due_date" TIMESTAMP,
            "case_id" UUID NOT NULL,
            "assignee_id" CHARACTER VARYING,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_437d9cd9" FOREIGN KEY ("case_id") REFERENCES "cases"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "task_dependencies" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "task_id" UUID NOT NULL,
            "depends_on_id" UUID NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            CONSTRAINT "FK_18f70887" FOREIGN KEY ("task_id") REFERENCES "tasks"("id"),
            CONSTRAINT "FK_3244b8d9" FOREIGN KEY ("depends_on_id") REFERENCES "tasks"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "task_history" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "task_id" UUID NOT NULL,
            "from_status" CHARACTER VARYING,
            "to_status" CHARACTER VARYING NOT NULL,
            "changed_by" CHARACTER VARYING NOT NULL,
            "changed_by_name" CHARACTER VARYING,
            "changed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "metadata" JSONB,
            CONSTRAINT "FK_292ab0" FOREIGN KEY ("task_id") REFERENCES "tasks"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_folders" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "case_id" UUID NOT NULL,
            "parent_folder_id" UUID,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "path" TEXT,
            CONSTRAINT "FK_d37fcec" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_25804715" FOREIGN KEY ("parent_folder_id") REFERENCES "document_folders"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "documents" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "name" CHARACTER VARYING NOT NULL,
            "description" TEXT,
            "case_id" UUID NOT NULL,
            "folder_id" UUID,
            "s3_key" CHARACTER VARYING NOT NULL,
            "s3_bucket" CHARACTER VARYING NOT NULL,
            "file_name" CHARACTER VARYING NOT NULL,
            "file_extension" CHARACTER VARYING,
            "mime_type" CHARACTER VARYING NOT NULL,
            "size_in_bytes" BIGINT NOT NULL,
            "checksum" CHARACTER VARYING NOT NULL,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "last_modified_by" CHARACTER VARYING NOT NULL,
            CONSTRAINT "FK_2a6255fd" FOREIGN KEY ("case_id") REFERENCES "cases"("id"),
            CONSTRAINT "FK_23aee23b" FOREIGN KEY ("folder_id") REFERENCES "document_folders"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_access" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID NOT NULL,
            "user_id" CHARACTER VARYING NOT NULL,
            "permission_level" CHARACTER VARYING NOT NULL DEFAULT 'read',
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "expires_at" TIMESTAMP,
            CONSTRAINT "FK_37fe29f8" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_audit" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID,
            "action_type" CHARACTER VARYING NOT NULL,
            "action_details" JSONB,
            "performed_by" CHARACTER VARYING NOT NULL,
            "performed_at" TIMESTAMP NOT NULL DEFAULT now(),
            "ip_address" CHARACTER VARYING,
            "user_agent" TEXT,
            "document_version_id" CHARACTER VARYING,
            CONSTRAINT "FK_3c86e5c9" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "document_workflows" (
            "id" UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
            "document_id" UUID NOT NULL,
            "workflow_type" CHARACTER VARYING NOT NULL,
            "current_state" CHARACTER VARYING NOT NULL,
            "workflow_data" JSONB,
            "created_by" CHARACTER VARYING NOT NULL,
            "created_at" TIMESTAMP NOT NULL DEFAULT now(),
            "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
            "completed_at" TIMESTAMP,
            "due_date" TIMESTAMP,
            "assigned_to" CHARACTER VARYING,
            CONSTRAINT "FK_541bfc90" FOREIGN KEY ("document_id") REFERENCES "documents"("id")
        )`);

        await queryRunner.query(`CREATE TABLE "user_roles" (
            "user_id" uuid NOT NULL,
            "role_id" uuid NOT NULL,
            CONSTRAINT "PK_user_roles" PRIMARY KEY ("user_id", "role_id"),
            CONSTRAINT "FK_user_roles_user_id"
                FOREIGN KEY ("user_id")
                REFERENCES "user_profiles"("id")
                ON DELETE CASCADE,
            CONSTRAINT "FK_user_roles_role_id"
                FOREIGN KEY ("role_id")
                REFERENCES "tenant_roles"("id")
                ON DELETE CASCADE
        )`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "user_roles"`);

        await queryRunner.query(`DROP TABLE "document_workflows"`);

        await queryRunner.query(`DROP TABLE "document_audit"`);

        await queryRunner.query(`DROP TABLE "document_access"`);

        await queryRunner.query(`DROP TABLE "documents"`);

        await queryRunner.query(`DROP TABLE "document_folders"`);

        await queryRunner.query(`DROP TABLE "task_history"`);

        await queryRunner.query(`DROP TABLE "task_dependencies"`);

        await queryRunner.query(`DROP TABLE "tasks"`);

        await queryRunner.query(`DROP TABLE "case_relations"`);

        await queryRunner.query(`DROP TABLE "case_assignments"`);

        await queryRunner.query(`DROP TABLE "case_events"`);

        await queryRunner.query(`DROP TABLE "case_contacts"`);

        await queryRunner.query(`DROP TABLE "case_audit"`);

        await queryRunner.query(`DROP TABLE "case_attachments"`);

        await queryRunner.query(`DROP TABLE "case_notes"`);

        await queryRunner.query(`DROP TABLE "cases"`);

        await queryRunner.query(`DROP TABLE "clients"`);

        await queryRunner.query(`DROP TABLE "user_profiles"`);

        await queryRunner.query(`DROP TABLE "tenant_roles"`);

        await queryRunner.query(`DROP TYPE "case_attachments_document_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_audit_action_enum"`);

        await queryRunner.query(`DROP TYPE "case_contacts_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_events_category_enum"`);

        await queryRunner.query(`DROP TYPE "case_events_type_enum"`);

        await queryRunner.query(`DROP TYPE "cases_status_enum"`);

        await queryRunner.query(`DROP TYPE "cases_priority_enum"`);

        await queryRunner.query(`DROP TYPE "cases_type_enum"`);

        await queryRunner.query(`DROP TYPE "case_relations_type_enum"`);

        await queryRunner.query(`DROP TYPE "task_status"`);

        await queryRunner.query(`DROP TYPE "task_priority"`);
    }
}
