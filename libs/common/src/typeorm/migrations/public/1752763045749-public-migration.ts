import { MigrationInterface, QueryRunner } from "typeorm";

export class PublicMigration1752763045749 implements MigrationInterface {
    name = 'PublicMigration1752763045749'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "system_roles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "permissions" jsonb, "enabled" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_e17f07bcbed1c457ff7fcc13797" UNIQUE ("name"), CONSTRAINT "PK_468b99ca2261e84113b6ec40814" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "system_users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "email" character varying NOT NULL, "password" character varying NOT NULL, "first_name" character varying, "last_name" character varying, "is_active" boolean NOT NULL DEFAULT true, "email_verified" boolean NOT NULL DEFAULT false, "remember_me" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_73dff187ed765e8403bf5fc911e" UNIQUE ("email"), CONSTRAINT "PK_cd8917a46de98ec75f9197911c0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "tenants" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "realm" character varying NOT NULL, "display_name" character varying, "admin_username" character varying, "admin_email" character varying, "admin_first_name" character varying, "admin_last_name" character varying, "registration_allowed" boolean NOT NULL DEFAULT false, "verify_email" boolean NOT NULL DEFAULT true, "remember_me" boolean NOT NULL DEFAULT true, "dedicated_realm_admin" boolean NOT NULL DEFAULT true, "client_id" character varying, "client_secret" character varying, "enabled" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_791b9b2a9b0c45c68edb806e56a" UNIQUE ("realm"), CONSTRAINT "UQ_0467a8fb232f628b5252e11bfcf" UNIQUE ("admin_email"), CONSTRAINT "PK_53be67a04681c66b87ee27c9321" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "products" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "price" numeric(10,2) NOT NULL, "stock_quantity" integer NOT NULL DEFAULT '0', "is_active" boolean NOT NULL DEFAULT true, "sku" character varying NOT NULL, "metadata" jsonb, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_c44ac33a05b144dd0d9ddcf9327" UNIQUE ("sku"), CONSTRAINT "PK_0806c755e0aca124e67c0cf6d7d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_system_roles" ("role_id" uuid NOT NULL, "user_id" uuid NOT NULL, CONSTRAINT "PK_78a8e896177b62c4e81724abc05" PRIMARY KEY ("role_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_3df0d816f256ac53b034283cc2" ON "user_system_roles" ("role_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_349311d5c6e8ae07fe8f6f4b24" ON "user_system_roles" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "user_tenants" ("user_id" uuid NOT NULL, "tenant_id" uuid NOT NULL, CONSTRAINT "PK_01847c3ffef489ea549f205d1ed" PRIMARY KEY ("user_id", "tenant_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_63a8ef4ed4fad61231cdfc3dc6" ON "user_tenants" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a1feca39273dfd9a32c7cc4153" ON "user_tenants" ("tenant_id") `);
        await queryRunner.query(`ALTER TABLE "user_system_roles" ADD CONSTRAINT "FK_3df0d816f256ac53b034283cc20" FOREIGN KEY ("role_id") REFERENCES "system_roles"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "user_system_roles" ADD CONSTRAINT "FK_349311d5c6e8ae07fe8f6f4b247" FOREIGN KEY ("user_id") REFERENCES "system_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_tenants" ADD CONSTRAINT "FK_63a8ef4ed4fad61231cdfc3dc63" FOREIGN KEY ("user_id") REFERENCES "system_users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "user_tenants" ADD CONSTRAINT "FK_a1feca39273dfd9a32c7cc4153c" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user_tenants" DROP CONSTRAINT "FK_a1feca39273dfd9a32c7cc4153c"`);
        await queryRunner.query(`ALTER TABLE "user_tenants" DROP CONSTRAINT "FK_63a8ef4ed4fad61231cdfc3dc63"`);
        await queryRunner.query(`ALTER TABLE "user_system_roles" DROP CONSTRAINT "FK_349311d5c6e8ae07fe8f6f4b247"`);
        await queryRunner.query(`ALTER TABLE "user_system_roles" DROP CONSTRAINT "FK_3df0d816f256ac53b034283cc20"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a1feca39273dfd9a32c7cc4153"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_63a8ef4ed4fad61231cdfc3dc6"`);
        await queryRunner.query(`DROP TABLE "user_tenants"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_349311d5c6e8ae07fe8f6f4b24"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_3df0d816f256ac53b034283cc2"`);
        await queryRunner.query(`DROP TABLE "user_system_roles"`);
        await queryRunner.query(`DROP TABLE "products"`);
        await queryRunner.query(`DROP TABLE "tenants"`);
        await queryRunner.query(`DROP TABLE "system_users"`);
        await queryRunner.query(`DROP TABLE "system_roles"`);
    }

}
