import { AppRole } from '@app/common/enums/roles.enums';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ROLE_GROUPS } from '@app/common/permissions/role-group-permissions.defaults';

/**
 * Service for managing role hierarchies and relationships in the role group system
 *
 * Role Group Hierarchy:
 * - SUPER_ADMIN: Has all privileges across all role groups
 * - {roleGroup}_admin: Admin privileges within a specific role group
 * - {roleGroup}_user: Normal user privileges within a specific role group
 *
 * Examples:
 * - conveyancers_admin > conveyancers_user
 * - finance_admin > finance_user
 * - SUPER_ADMIN > all role group admins > all role group users
 */
@Injectable()
export class RoleHierarchyService {
    private readonly logger = new Logger(RoleHierarchyService.name);
    private readonly roleHierarchy: Map<string, string[]> = new Map();

    constructor(private configService: ConfigService) {
        this.initializeRoleHierarchy();
    }

    /**
     * Initialize role hierarchy from configuration
     */
    private initializeRoleHierarchy(): void {
        try {
            const hierarchyConfig = this.configService.get<Record<string, string[]>>(
                'roles.hierarchy',
                {}
            );

            Object.entries(hierarchyConfig).forEach(([role, impliedRoles]) => {
                this.roleHierarchy.set(role, impliedRoles || []);
            });

            if (this.roleHierarchy.size <= 0) {
                this.setDefaultHierarchy();
            }
        } catch (error) {
            this.logger.error(`Failed to initialize role hierarchy: ${error.message}`);
            // Set default hierarchy if configuration fails
            this.setDefaultHierarchy();
        }
    }

    /**
     * Set default role hierarchy based on role groups
     * Hierarchy: SUPER_ADMIN > role_group_admin > role_group_user
     */
    private setDefaultHierarchy(): void {
        // SUPER_ADMIN has access to all role group admin privileges
        const allRoleGroupAdmins: string[] = [];

        // Build hierarchy for each role group
        for (const group of ROLE_GROUPS) {
            const adminRole = `${group.key}_admin`;
            const userRole = `${group.key}_user`;

            // Role group admin inherits user privileges within the same group
            this.roleHierarchy.set(adminRole, [userRole]);
            allRoleGroupAdmins.push(adminRole);
        }

        // SUPER_ADMIN inherits all role group admin privileges
        this.roleHierarchy.set(AppRole.SUPER_ADMIN, allRoleGroupAdmins);

        this.logger.log('Role group hierarchy set', {
            superAdminInherits: allRoleGroupAdmins,
            roleGroups: ROLE_GROUPS.map(g => ({
                admin: `${g.key}_admin`,
                user: `${g.key}_user`
            }))
        });
    }

    /**
     * Expand a set of roles to include all implied roles based on role group hierarchy
     * @param roles Direct roles assigned to the user
     * @returns Expanded set of roles including all implied roles
     */
    expandRoles(roles: string[]): string[] {
        this.initializeRoleHierarchy();
        const result = new Set<string>(roles);
        let added = true;
        let iterations = 0;
        const maxIterations = 10; // Prevent infinite loops

        this.logger.debug(`Expanding roles: ${roles.join(', ')}`);

        // Keep expanding roles until no new roles are added
        while (added && iterations < maxIterations) {
            added = false;
            iterations++;

            // Create a copy of current result to avoid concurrent modification
            const currentRoles = Array.from(result);

            for (const role of currentRoles) {
                const impliedRoles = this.roleHierarchy.get(role) || [];

                for (const impliedRole of impliedRoles) {
                    if (!result.has(impliedRole)) {
                        result.add(impliedRole);
                        added = true;
                        this.logger.debug(`Role ${role} implies ${impliedRole}`);
                    }
                }
            }
        }

        const expandedRoles = Array.from(result);
        this.logger.debug(`Expanded roles after ${iterations} iterations: ${expandedRoles.join(', ')}`);

        return expandedRoles;
    }

    /**
     * Check if a user with the given roles has all required roles
     * @param userRoles User's roles including implied roles
     * @param requiredRoles Roles required for access
     * @returns Whether the user has all the required roles
     */
    hasAllRoles(userRoles: string[], requiredRoles: string[]): boolean {
        return requiredRoles.every((role) => userRoles.includes(role));
    }

    /**
     * Check if a user with the given roles has any of the required roles
     * @param userRoles User's roles including implied roles
     * @param requiredRoles Roles required for access
     * @returns Whether the user has any of the required roles
     */
    hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
        return requiredRoles.some((role) => userRoles.includes(role));
    }

    /**
     * Check if a user is a super admin
     * @param userRoles User's roles
     * @returns Whether the user is a super admin
     */
    isSuperAdmin(userRoles: string[]): boolean {
        return userRoles.includes(AppRole.SUPER_ADMIN);
    }

    /**
     * Check if a user is an admin of any role group
     * @param userRoles User's roles
     * @returns Whether the user is a role group admin
     */
    isRoleGroupAdmin(userRoles: string[]): boolean {
        return userRoles.some(role => role.endsWith('_admin'));
    }

    /**
     * Get the role group key from a role name
     * @param roleName Role name (e.g., 'conveyancers_admin', 'finance_user')
     * @returns Role group key (e.g., 'conveyancers', 'finance') or null if not a role group role
     */
    getRoleGroupKey(roleName: string): string | null {
        if (roleName.endsWith('_admin') || roleName.endsWith('_user')) {
            return roleName.replace(/_admin$|_user$/, '');
        }
        return null;
    }

    /**
     * Check if a user is an admin of a specific role group
     * @param userRoles User's roles
     * @param roleGroupKey Role group key (e.g., 'conveyancers')
     * @returns Whether the user is an admin of the specified role group
     */
    isAdminOfRoleGroup(userRoles: string[], roleGroupKey: string): boolean {
        return userRoles.includes(`${roleGroupKey}_admin`) || this.isSuperAdmin(userRoles);
    }

    /**
     * Check if a user belongs to a specific role group (either as admin or user)
     * @param userRoles User's roles
     * @param roleGroupKey Role group key (e.g., 'conveyancers')
     * @returns Whether the user belongs to the role group
     */
    belongsToRoleGroup(userRoles: string[], roleGroupKey: string): boolean {
        return userRoles.includes(`${roleGroupKey}_admin`) ||
               userRoles.includes(`${roleGroupKey}_user`) ||
               this.isSuperAdmin(userRoles);
    }

    /**
     * Get all role groups that a user belongs to
     * @param userRoles User's roles
     * @returns Array of role group keys the user belongs to
     */
    getUserRoleGroups(userRoles: string[]): string[] {
        const roleGroups = new Set<string>();

        // Super admin belongs to all role groups
        if (this.isSuperAdmin(userRoles)) {
            return ROLE_GROUPS.map(group => group.key);
        }

        // Check each role to see if it's a role group role
        for (const role of userRoles) {
            const roleGroupKey = this.getRoleGroupKey(role);
            if (roleGroupKey) {
                roleGroups.add(roleGroupKey);
            }
        }

        return Array.from(roleGroups);
    }

    /**
     * Get the hierarchy level of a role (lower number = higher privilege)
     * @param roleName Role name
     * @returns Hierarchy level (0 = highest, higher numbers = lower privilege)
     */
    getRoleHierarchyLevel(roleName: string): number {
        if (roleName === AppRole.SUPER_ADMIN) return 0;
        if (roleName.endsWith('_admin')) return 1;
        if (roleName.endsWith('_user')) return 2;
        return 999; // Unknown roles get lowest priority
    }
}
