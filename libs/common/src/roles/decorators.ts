import { SetMetadata } from '@nestjs/common';

/**
 * Metadata keys for authorization
 */
export const ROLES_KEY = 'roles';
export const REQUIRE_ALL_ROLES_KEY = 'requireAllRoles';
export const USER_ATTRIBUTES_KEY = 'userAttributes';
export const IS_PUBLIC_KEY = 'isPublic';

/**
 * RBAC Decorators
 */

/**
 * Require any of the specified roles for access
 * This is the default behavior when specifying roles
 * @param roles Array of roles, any of which grants access
 */
export const HasAnyRole = (...roles: string[]) => SetMetadata(ROLES_KEY, roles);

/**
 * Require all of the specified roles for access
 * @param roles Array of roles, all of which are required for access
 */
export const HasAllRoles = (...roles: string[]) => {
    return (target: any, key?: string, descriptor?: any) => {
        SetMetadata(ROLES_KEY, roles)(target, key as string, descriptor);
        SetMetadata(REQUIRE_ALL_ROLES_KEY, true)(target, key as string, descriptor);
    };
};

/**
 * User attribute checks
 */

/**
 * Check user attributes for access control
 * @param attributeChecks Map of attribute names to required values or validation functions
 */
export const CheckUserAttributes = (attributeChecks: Record<string, any>) =>
    SetMetadata(USER_ATTRIBUTES_KEY, attributeChecks);

/**
 * Public route decorator - bypasses authentication
 */
export const IsPublic = () => SetMetadata(IS_PUBLIC_KEY, true);

/**
 * Convenience aliases for common patterns
 */
export const Roles = HasAnyRole;
