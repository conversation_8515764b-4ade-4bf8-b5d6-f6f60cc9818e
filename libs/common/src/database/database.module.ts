import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { ConfigModule } from '../config/config.module';

@Module({
    imports: [
        TypeOrmModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                const dbConfig = configService.get('database') || {};

                return {
                    type: 'postgres',
                    host: dbConfig.host,
                    port: dbConfig.port,
                    username: dbConfig.username,
                    password: dbConfig.password,
                    database: dbConfig.database,
                    entities: [__dirname + '/../**/*.entity{.ts,.js}'],
                    synchronize: configService.get('app.isDevelopment'),
                    ssl: dbConfig.ssl || false,
                    extra: {
                        max: dbConfig.maxConnections,
                        idleTimeoutMillis: dbConfig.idleTimeoutMs,
                        connectionTimeoutMillis: dbConfig.connectionTimeoutMs
                    }
                };
            }
        })
    ],
    exports: [TypeOrmModule]
})
export class DatabaseModule {}
