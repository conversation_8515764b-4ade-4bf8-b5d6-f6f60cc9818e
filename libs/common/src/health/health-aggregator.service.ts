import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HealthService } from './health.service';
import { HttpService } from '@nestjs/axios';
import { catchError, firstValueFrom } from 'rxjs';
import { HealthState, ServiceReport, HealthAggregateResult, aggregateStatus } from './health.types';
import { Config } from '../config/config';
import { HttpStatusCode } from '../enums/http-status.enum';

@Injectable()
export class HealthAggregatorService {
    private readonly logger = new Logger(HealthAggregatorService.name);

    /** micro‑services we poll */
    private readonly services: { name: string; url: string }[];

    constructor(
        private readonly config: ConfigService,
        private readonly health: HealthService,
        private readonly http: HttpService
    ) {
        // Use the correct prefixes from Config class
        const buildUrl = (portEnv: string, prefix: string) =>
            `http://${this.config.get('HOST', 'localhost')}:${this.config.get<number>(portEnv)}/${prefix}/health`;

        this.services = [
            {
                name: 'communication',
                url: buildUrl('COMMUNICATION_PORT', Config.COMMUNICATION_PREFIX)
            },
            {
                name: 'document-engine',
                url: buildUrl('DOCUMENT_ENGINE_PORT', Config.DOCUMENT_ENGINE_PREFIX)
            },
            {
                name: 'auth',
                url: buildUrl('AUTH_PORT', Config.AUTH_PREFIX)
            },
            {
                name: 'case-management',
                url: buildUrl('CASE_MANAGEMENT_PORT', Config.CASE_MANAGEMENT_PREFIX)
            }
        ];

        this.logger.log(
            `Health targets: ${this.services.map((s) => `${s.name}→${s.url}`).join(', ')}`
        );
    }

    /** ---------- public API ---------- */
    async getHealth(): Promise<HealthAggregateResult> {
        const coreReport = await this.checkCore();
        const svcReports = await this.checkServices();

        const overall = aggregateStatus([coreReport, ...Object.values(svcReports)]);

        return { overall, core: coreReport, services: svcReports };
    }

    /** ---------- internals ---------- */
    private async checkCore(): Promise<ServiceReport> {
        try {
            const db = await this.health.checkDbHealth(); // ← Terminus call
            const dbState =
                db.error?.database?.status === 'down' ? HealthState.DOWN : HealthState.UP;

            return {
                name: 'core',
                api: HealthState.UP,
                db: dbState,
                details: db.details
            };
        } catch (err) {
            this.logger.error(`Core health failed: ${err.message}`);
            return {
                name: 'core',
                api: HealthState.ERROR,
                error: err.message
            };
        }
    }

    private async checkServices(): Promise<Record<string, ServiceReport>> {
        const results = await Promise.allSettled(
            this.services.map((svc) => this.callHealthEndpoint(svc))
        );

        return results.reduce<Record<string, ServiceReport>>((acc, res, idx) => {
            const name = this.services[idx].name;
            acc[name] =
                res.status === 'fulfilled'
                    ? res.value
                    : { name, api: HealthState.ERROR, error: res.reason as string };
            return acc;
        }, {});
    }

    private async callHealthEndpoint(svc: { name: string; url: string }): Promise<ServiceReport> {
        this.logger.debug(`▶ Health check: ${svc.name}`);
        try {
            const resp = await firstValueFrom(
                this.http.get(svc.url).pipe(
                    catchError((err) => {
                        throw this.mapAxiosError(err);
                    })
                )
            );

            // expecting Terminus‑style payload → treat 200 as UP
            return {
                name: svc.name,
                api: HealthState.UP,
                details: resp.data
            };
        } catch (err) {
            return { name: svc.name, ...err };
        }
    }

    /** Converts Http / network errors into a standard ServiceReport‑ish object */
    private mapAxiosError(err: any): ServiceReport {
        if (err.code === 'ECONNREFUSED') {
            return { api: HealthState.DOWN, error: 'Connection refused' } as any;
        }
        if (err.response?.status === HttpStatusCode.NOT_FOUND) {
            return { api: HealthState.WARNING, error: 'Health endpoint 404' } as any;
        }
        return { api: HealthState.ERROR, error: err.message } as any;
    }

    /** --------------------------------------------------------
     *  DB‑only snapshot  (used by GET /health/db)
     *  ----------------------------------------------------- */
    async getDbSnapshot(): Promise<{ status: string; databases: Record<string, any> }> {
        // Only check the core database - no HTTP requests to other services
        const coreDb = await this.checkCoreDb();

        // Build the response with just the core DB status
        const databases = {};

        // Add core status
        databases['core'] = {
            status: coreDb.db || 'unknown'
        };

        // Add error if present
        if (coreDb.error) {
            databases['core'].error = coreDb.error;
        }

        // Overall status is determined by core DB status
        const overallStatus = coreDb?.db === HealthState.DOWN ? 'down' : 'up';

        return {
            status: overallStatus,
            databases
        };
    }

    /** only ping the core DB (no HTTP) */
    private async checkCoreDb(): Promise<ServiceReport> {
        try {
            const dbHealth = await this.health.checkDbHealth();
            const state =
                dbHealth.error?.database?.status === 'down' ? HealthState.DOWN : HealthState.UP;

            return {
                name: 'core',
                api: HealthState.UP,
                db: state,
                error:
                    state === HealthState.DOWN
                        ? 'Database connection not configured or unavailable'
                        : undefined
            };
        } catch (e) {
            return {
                name: 'core',
                api: HealthState.ERROR,
                db: HealthState.ERROR,
                error: e.message
            };
        }
    }

    getHealthStatus() {
        return {
            status: 'ok',
            details: {
                api: {
                    status: 'up',
                    message: 'API is operational'
                }
            }
        };
    }
}
