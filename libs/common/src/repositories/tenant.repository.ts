import { Injectable } from '@nestjs/common';
import { Tenant } from '@app/common/typeorm/entities/public/tenant.entity';
import { CreateTenantDto } from '../../../../apps/auth/src/dto/create-tenant.dto';
import * as crypto from 'crypto';
import {
    BaseTenantRepository,
    TenantConnectionService,
    TenantContextService
} from '@app/common/multi-tenancy';

@Injectable()
export class TenantRepository extends BaseTenantRepository<Tenant> {
    constructor(
        protected readonly tenantConnectionService: TenantConnectionService,
        protected readonly tenantContextService: TenantContextService
    ) {
        super(Tenant, tenantContextService, tenantConnectionService, true);
    }

    /**
     * Finds a tenant by realm name
     * @param realm The realm name to search for
     * @returns The tenant entity or null if not found
     */
    async findByRealm(realm: string): Promise<Tenant | null> {
        this.logger.log(`Finding tenant by realm: ${realm}`);
        const data = await this.findOne({ where: { realm } });

        this.logger.log(`Found tenant: ${data ? data.id : 'null'}`);
        return data;
    }

    async findRealmById(id: string): Promise<Tenant | null> {
        const data = await this.findOne({ where: { id } });
        return data;
    }

    /**
     * Creates a new tenant entity from the provided DTO and client information
     * @param createTenantDto The tenant creation DTO
     * @param clientId The client ID for the tenant
     * @param clientSecret The client secret (optional for public clients)
     * @returns The created tenant entity
     */
    async createTenant(
        createTenantDto: CreateTenantDto,
        clientId: string,
        clientSecret?: string
    ): Promise<Tenant> {
        const tenant = new Tenant();
        tenant.realm = createTenantDto.realm;
        tenant.displayName = createTenantDto.displayName;
        tenant.adminUsername = createTenantDto.adminUsername;
        tenant.adminEmail = createTenantDto.adminEmail;
        tenant.clientId = clientId;
        tenant.clientSecret = clientSecret ?? crypto.randomUUID();
        tenant.enabled = true;
        return this.save(tenant);
    }

    /**
     * Updates a tenant's properties
     * @param realm The realm name of the tenant to update
     * @param properties Object containing the properties to update
     * @returns The updated tenant entity
     */
    async updateTenant(realm: string, properties: Partial<Tenant>): Promise<Tenant | null> {
        const tenant = await this.findByRealm(realm);

        if (!tenant) {
            return null;
        }

        Object.assign(tenant, properties);
        return this.save(tenant);
    }

    /**
     * Enables or disables a tenant
     * @param realm The realm name
     * @param enabled The enabled status
     * @returns The updated tenant entity
     */
    async setTenantStatus(realm: string, enabled: boolean): Promise<Tenant | null> {
        return this.updateTenant(realm, { enabled });
    }

    /**
     * Updates a tenant's client credentials
     * @param realm The realm name
     * @param clientId The new client ID
     * @param clientSecret The new client secret
     * @returns The updated tenant entity
     */
    async updateClientCredentials(
        realm: string,
        clientId: string,
        clientSecret: string
    ): Promise<Tenant | null> {
        return this.updateTenant(realm, { clientId, clientSecret });
    }

    /**
     * Deletes a tenant by realm name
     * @param realm The realm name
     * @returns True if the tenant was deleted, false otherwise
     */
    async deleteTenantByRealm(realm: string): Promise<boolean> {
        // const result = await this.remove({ id: realm });
        // return result.affected !== undefined && result.affected !== null && result.affected > 0;
        const result = await this.removeById(realm);
        return result;
    }

    /**
     * Gets all enabled tenants
     * @returns Array of enabled tenant entities
     */
    async findAllEnabledTenants(): Promise<Tenant[]> {
        return this.find({ where: { enabled: true } });
    }
}
