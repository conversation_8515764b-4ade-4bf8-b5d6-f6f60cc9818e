import { forwardRef, Module } from '@nestjs/common';
import { RoleRepository } from '@app/common/repositories/role.repository';
import { PublicSchemaMigrationService } from './typeorm/migrations/public-schema.migrations';
import { RoleHierarchyService } from '@app/common/roles/hierarchy';
import { TenantSchemaMigrationService } from './typeorm/migrations/tenant-schema.migrations';
import { TenantRoleRepository } from '@app/common/repositories/tenant-role.repository';
import { SystemRoleRepository } from '@app/common/repositories/system-role.repository';
import { PublicUserRepository } from '@app/common/repositories/public-user.repository';
import { UserRepository } from '@app/common/repositories/user.repository';
import { TenantRepository } from '@app/common/repositories/tenant.repository';
import { KeycloakHttpService } from 'apps/auth/src/services/keycloak-http.service';
import { KeycloakService } from 'apps/auth/src/services/keycloak.service';
import { RepositoriesModule } from '@app/common/repositories/repositories.module';
import { RolesGuard } from './guards/roles.guard';
import { TenantContextService } from './multi-tenancy/tenant-context.service';
import { TenantConnectionService } from './multi-tenancy/tenant-connection.service';
import { AuthService } from 'apps/auth/src/services/auth.service';
import { JwtGuard } from './guards/jwt.guard';
import { AwsConfigService } from './config/aws.config';
import { RedisModule } from './cache/redis.module';
import { ConfigModule } from './config/config.module';
import { ALL_ENTITIES } from './typeorm/entities';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuditService } from './audit/audit.service';
import { CacheService } from './cache/cache.service';
import { RoleGroupPermissionService } from './permissions/role-group-permission.service';
import { PermissionGuard } from './guards/permission.guard';
import { ConveyancerCaseGuard } from './guards/conveyancer-case.guard';

@Module({
    imports: [
        ConfigModule,
        TypeOrmModule.forRoot({
            name: 'default',
            type: 'postgres',
            host: process.env.POSTGRES_HOST,
            port: parseInt(process.env.POSTGRES_PORT || '5432', 10),
            username: process.env.POSTGRES_USER,
            password: String(process.env.POSTGRES_PASSWORD),
            database: process.env.POSTGRES_DB,
            schema: 'public',
            entities: ALL_ENTITIES,
            synchronize: false,
            logging: true
        }),
        forwardRef(() => RepositoriesModule),
        RedisModule
    ],
    controllers: [],
    providers: [
        KeycloakService,
        KeycloakHttpService,
        TenantRepository,
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantSchemaMigrationService,
        RoleHierarchyService,
        AuditService,
        CacheService,
        PublicSchemaMigrationService,
        RolesGuard,
        TenantContextService,
        TenantConnectionService,
        AuthService,
        JwtGuard,
        AwsConfigService,
        RoleGroupPermissionService,
        PermissionGuard,
        ConveyancerCaseGuard
    ],
    exports: [
        ConfigModule,
        TenantRepository,
        UserRepository,
        PublicUserRepository,
        SystemRoleRepository,
        TenantRoleRepository,
        RoleRepository,
        TenantSchemaMigrationService,
        KeycloakService,
        KeycloakHttpService,
        RepositoriesModule,
        RolesGuard,
        AuditService,
        CacheService,
        PublicSchemaMigrationService,
        RoleHierarchyService,
        TenantContextService,
        TenantConnectionService,
        AuthService,
        JwtGuard,
        AwsConfigService,
        RedisModule,
        RoleGroupPermissionService,
        PermissionGuard,
        ConveyancerCaseGuard
    ]
})
export class CommonModule {}
