import { Permission } from './enums/permission.enum';
import { ResourceType } from './permission.constants';

/**
 * Central definition of all role groups and their default permissions.
 * Extendable for more resources in the future.
 */
export interface RoleGroupDefaultPermissions {
    [resource: string]: Permission[];
}

export interface RoleGroupDefinition {
    key: string; // e.g. 'conveyancers', 'finance', etc.
    label: string;
    defaultPermissions: RoleGroupDefaultPermissions;
}

export const ROLE_GROUPS: RoleGroupDefinition[] = [
    {
        key: 'conveyancers',
        label: 'Conveyancers',
        defaultPermissions: {
            [ResourceType.CASE]: [Permission.READ, Permission.UPDATE],
            [ResourceType.DOCUMENT]: [Permission.READ, Permission.CREATE, Permission.UPDATE]
        }
    },
    {
        key: 'finance',
        label: 'Finance',
        defaultPermissions: {
            [ResourceType.CASE]: [Permission.READ],
            [ResourceType.DOCUMENT]: [Permission.READ]
        }
    }
    // Add more groups as needed
];
