import { SetMetadata } from '@nestjs/common';
import { AppRole } from '@app/common/enums/roles.enums';

/**
 * Metadata keys for role group authorization
 */
export const ROLE_GROUP_KEY = 'roleGroup';
export const ROLE_GROUP_ADMIN_KEY = 'roleGroupAdmin';
export const ROLE_GROUP_USER_KEY = 'roleGroupUser';
export const REQUIRE_ROLE_GROUP_ADMIN_KEY = 'requireRoleGroupAdmin';

/**
 * Role Group Decorators for the new role group system
 */

/**
 * Require Super Admin access
 * Usage: @RequireSuperAdmin()
 */
export const RequireSuperAdmin = () => SetMetadata('roles', [AppRole.SUPER_ADMIN]);

/**
 * Require access to a specific role group (either admin or user)
 * Usage: @RequireRoleGroup('conveyancers')
 */
export const RequireRoleGroup = (roleGroupKey: string) => 
    SetMetadata(ROLE_GROUP_KEY, roleGroupKey);

/**
 * Require admin access to a specific role group
 * Usage: @RequireRoleGroupAdmin('conveyancers')
 */
export const RequireRoleGroupAdmin = (roleGroupKey: string) => 
    SetMetadata(ROLE_GROUP_ADMIN_KEY, roleGroupKey);

/**
 * Require user access to a specific role group (includes admin)
 * Usage: @RequireRoleGroupUser('conveyancers')
 */
export const RequireRoleGroupUser = (roleGroupKey: string) => 
    SetMetadata(ROLE_GROUP_USER_KEY, roleGroupKey);

/**
 * Require admin access to any role group
 * Usage: @RequireAnyRoleGroupAdmin()
 */
export const RequireAnyRoleGroupAdmin = () => 
    SetMetadata(REQUIRE_ROLE_GROUP_ADMIN_KEY, true);

/**
 * Allow access to multiple role groups (any of them)
 * Usage: @AllowRoleGroups(['conveyancers', 'finance'])
 */
export const AllowRoleGroups = (roleGroupKeys: string[]) => 
    SetMetadata('allowedRoleGroups', roleGroupKeys);

/**
 * Allow admin access to multiple role groups (any of them)
 * Usage: @AllowRoleGroupAdmins(['conveyancers', 'finance'])
 */
export const AllowRoleGroupAdmins = (roleGroupKeys: string[]) => 
    SetMetadata('allowedRoleGroupAdmins', roleGroupKeys);

/**
 * Combined decorators for common scenarios
 */

/**
 * Super Admin or specific role group admin
 * Usage: @SuperAdminOrRoleGroupAdmin('conveyancers')
 */
export function SuperAdminOrRoleGroupAdmin(roleGroupKey: string) {
    return (target: any, key?: string, descriptor?: any) => {
        SetMetadata('roles', [AppRole.SUPER_ADMIN])(target, key as string, descriptor);
        SetMetadata(ROLE_GROUP_ADMIN_KEY, roleGroupKey)(target, key as string, descriptor);
    };
}

/**
 * Super Admin or any role group admin
 * Usage: @SuperAdminOrAnyRoleGroupAdmin()
 */
export function SuperAdminOrAnyRoleGroupAdmin() {
    return (target: any, key?: string, descriptor?: any) => {
        SetMetadata('roles', [AppRole.SUPER_ADMIN])(target, key as string, descriptor);
        SetMetadata(REQUIRE_ROLE_GROUP_ADMIN_KEY, true)(target, key as string, descriptor);
    };
}

/**
 * Super Admin or role group access (admin or user)
 * Usage: @SuperAdminOrRoleGroup('conveyancers')
 */
export function SuperAdminOrRoleGroup(roleGroupKey: string) {
    return (target: any, key?: string, descriptor?: any) => {
        SetMetadata('roles', [AppRole.SUPER_ADMIN])(target, key as string, descriptor);
        SetMetadata(ROLE_GROUP_KEY, roleGroupKey)(target, key as string, descriptor);
    };
}

/**
 * Convenience decorators for specific role groups
 */
export const RequireConveyancers = () => RequireRoleGroup('conveyancers');
export const RequireConveyancersAdmin = () => RequireRoleGroupAdmin('conveyancers');
export const RequireFinance = () => RequireRoleGroup('finance');
export const RequireFinanceAdmin = () => RequireRoleGroupAdmin('finance');

/**
 * Convenience decorators for common patterns
 */
export const ConveyancersOnly = () => SuperAdminOrRoleGroup('conveyancers');
export const ConveyancersAdminOnly = () => SuperAdminOrRoleGroupAdmin('conveyancers');
export const FinanceOnly = () => SuperAdminOrRoleGroup('finance');
export const FinanceAdminOnly = () => SuperAdminOrRoleGroupAdmin('finance');
