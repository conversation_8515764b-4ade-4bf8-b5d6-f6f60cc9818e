import { Module } from '@nestjs/common';

import { CommonModule } from '../common.module';
import { RepositoriesModule } from '../repositories/repositories.module';
import { AuthService } from 'apps/auth/src/services/auth.service';
import { JwtGuard } from '../guards/jwt.guard';

/**
 * Shared AuthModule that can be imported by other services
 */
@Module({
    imports: [CommonModule, RepositoriesModule],
    providers: [AuthService, JwtGuard],
    exports: [AuthService, JwtGuard]
})
export class AuthModule {}
